name: CI
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x, 22.x]

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm ci
    - run: npm run build
    - run: npm test


  publish:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    needs: build
    permissions:
      contents: write
      issues: write
      pull-requests: write
      packages: write
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '22.x'
        registry-url: 'https://registry.npmjs.org'
    - run: npm ci
    - name: Setup .npmrc file
      run: |
        echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > .npmrc
        echo "registry=https://registry.npmjs.org/" >> .npmrc
        echo "always-auth=true" >> .npmrc
    - name: Release
      env:
        GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      run: npx semantic-release