{"name": "@tsavo/creatify-mcp", "version": "1.0.0", "description": "MCP Server that exposes Creatify AI API capabilities as MCP tools for AI assistants and agents", "main": "dist/index.js", "bin": {"creatify-mcp": "dist/index.js"}, "type": "module", "scripts": {"build": "tsup", "dev": "tsup --watch", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.ts\"", "check": "npm run type-check && npm run lint && npm run format", "prepublishOnly": "npm run build"}, "keywords": ["mcp", "model-context-protocol", "creatify", "ai", "video-generation", "avatar", "text-to-speech", "url-to-video", "ai-editing", "claude", "anthropic"], "author": {"name": "T Savo", "email": "<EMAIL>", "url": "https://www.horizon-city.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TSavo/creatify-mcp.git"}, "bugs": {"url": "https://github.com/TSavo/creatify-mcp/issues"}, "homepage": "https://github.com/TSavo/creatify-mcp#readme", "publishConfig": {"access": "public"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"@tsavo/creatify-api-ts": "^1.1.2", "@modelcontextprotocol/sdk": "^1.12.0", "zod": "^3.22.4"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^11.1.0", "@semantic-release/github": "^9.2.6", "@semantic-release/npm": "^11.0.2", "@semantic-release/release-notes-generator": "^12.1.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "prettier": "^3.5.3", "semantic-release": "^24.2.4", "tsup": "^8.0.0", "typescript": "^5.3.2", "vitest": "^1.0.0"}, "files": ["dist", "README.md", "LICENSE"]}