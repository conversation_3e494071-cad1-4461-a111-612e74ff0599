"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_layout_DebugDisplay_tsx";
exports.ids = ["_pages-dir-node_components_layout_DebugDisplay_tsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/layout/DebugDisplay.tsx":
/*!********************************************!*\
  !*** ./components/layout/DebugDisplay.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_access_badges__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/access-badges */ \"(pages-dir-node)/./utils/access-badges.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_access_badges__WEBPACK_IMPORTED_MODULE_3__]);\n_utils_access_badges__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Client-side only KeysDisplay to avoid hydration errors\nconst KeysDisplay = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.resolve(()=>{\n        const [hasAccess, setHasAccess] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n        react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n            \"KeysDisplay.useEffect\": ()=>{\n                // Check if user has any badges\n                const checkAccess = {\n                    \"KeysDisplay.useEffect.checkAccess\": async ()=>{\n                        const hasAccess = (0,_utils_access_badges__WEBPACK_IMPORTED_MODULE_3__.hasAnyBadge)();\n                        setHasAccess(hasAccess);\n                    }\n                }[\"KeysDisplay.useEffect.checkAccess\"];\n                checkAccess();\n                // Only set up interval if we're not on a story page\n                const isStoryPage = window.location.pathname.includes('/stories/');\n                if (!isStoryPage) {\n                    // Update access status periodically\n                    const interval = setInterval(checkAccess, 1000);\n                    return ({\n                        \"KeysDisplay.useEffect\": ()=>clearInterval(interval)\n                    })[\"KeysDisplay.useEffect\"];\n                }\n            }\n        }[\"KeysDisplay.useEffect\"], []);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                color: 'white',\n                fontSize: '8px',\n                textAlign: 'center',\n                marginTop: '5px'\n            },\n            children: [\n                \"Paydata Access: \",\n                hasAccess ? 'Granted' : 'Denied'\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n            lineNumber: 29,\n            columnNumber: 5\n        }, undefined);\n    }), {\n    ssr: false\n});\nconst DebugDisplay = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: '10px',\n            left: '10px',\n            zIndex: 9999,\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '5px',\n            background: 'rgba(0, 0, 0, 0.7)',\n            padding: '5px',\n            borderRadius: '4px',\n            border: '1px solid rgba(76, 29, 149, 0.5)',\n            maxWidth: '150px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '10px',\n                    textAlign: 'center',\n                    marginBottom: '5px',\n                    fontWeight: 'bold'\n                },\n                children: \"Paydata Debug\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KeysDisplay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DebugDisplay);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/layout/DebugDisplay.tsx\n");

/***/ })

};
;