"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_ui_DebugControls_tsx";
exports.ids = ["_pages-dir-node_components_ui_DebugControls_tsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/ui/DebugControls.tsx":
/*!*****************************************!*\
  !*** ./components/ui/DebugControls.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugControls)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/debug-manager */ \"(pages-dir-node)/./utils/debug-manager.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__]);\n_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n/**\n * Debug Controls Component\n *\n * This component provides a UI for controlling debug settings.\n * It's hidden by default and can be activated with a special key combination.\n */ function DebugControls() {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [debugState, setDebugState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        enabled: false,\n        level: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.NONE\n    });\n    // Initialize state from debug manager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DebugControls.useEffect\": ()=>{\n            // Check for debug activation key combination\n            const handleKeyDown = {\n                \"DebugControls.useEffect.handleKeyDown\": (e)=>{\n                    // Ctrl+Shift+D to toggle debug controls\n                    if (e.ctrlKey && e.shiftKey && e.key === 'D') {\n                        setIsVisible({\n                            \"DebugControls.useEffect.handleKeyDown\": (prev)=>!prev\n                        }[\"DebugControls.useEffect.handleKeyDown\"]);\n                        e.preventDefault();\n                    }\n                }\n            }[\"DebugControls.useEffect.handleKeyDown\"];\n            window.addEventListener('keydown', handleKeyDown);\n            // Update state from debug manager\n            const state = (0,_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.getDebugState)();\n            setDebugState(state);\n            return ({\n                \"DebugControls.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"DebugControls.useEffect\"];\n        }\n    }[\"DebugControls.useEffect\"], []);\n    // Handle debug enable/disable\n    const handleToggleDebug = ()=>{\n        const newEnabled = !debugState.enabled;\n        (0,_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.setDebugEnabled)(newEnabled);\n        setDebugState((prev)=>({\n                ...prev,\n                enabled: newEnabled\n            }));\n    };\n    // Handle debug level change\n    const handleLevelChange = (e)=>{\n        const level = parseInt(e.target.value);\n        (0,_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.setDebugLevel)(level);\n        setDebugState((prev)=>({\n                ...prev,\n                level\n            }));\n    };\n    // Handle clearing all badges\n    const handleClearBadges = ()=>{\n        if (window.confirm('Are you sure you want to clear all access badges? This will reset your progress.')) {\n            _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.BadgeDebugger.clearAllBadges();\n            window.location.reload();\n        }\n    };\n    // Handle viewing all badges\n    const handleViewBadges = ()=>{\n        _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.BadgeDebugger.logAllBadges();\n        alert('Badges logged to console. Check browser console (F12) to view them.');\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            bottom: '20px',\n            right: '20px',\n            zIndex: 9999,\n            backgroundColor: '#1A202C',\n            padding: '12px',\n            borderRadius: '6px',\n            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n            border: '1px solid #2D3748',\n            maxWidth: '300px',\n            color: 'white',\n            fontFamily: 'sans-serif'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontWeight: 'bold',\n                    marginBottom: '8px',\n                    color: '#4FD1C5'\n                },\n                children: \"Debug Controls (Ctrl+Shift+D)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '12px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"debug-toggle\",\n                                style: {\n                                    fontSize: '14px'\n                                },\n                                children: \"Debug Mode\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"debug-toggle\",\n                                type: \"checkbox\",\n                                checked: debugState.enabled,\n                                onChange: handleToggleDebug\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '4px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"debug-level\",\n                                style: {\n                                    fontSize: '14px'\n                                },\n                                children: \"Debug Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"debug-level\",\n                                value: debugState.level,\n                                onChange: handleLevelChange,\n                                disabled: !debugState.enabled,\n                                style: {\n                                    padding: '4px 8px',\n                                    backgroundColor: '#2D3748',\n                                    border: '1px solid #4A5568',\n                                    borderRadius: '4px',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.NONE,\n                                        children: \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.ERROR,\n                                        children: \"Error\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.WARN,\n                                        children: \"Warning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.INFO,\n                                        children: \"Info\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.VERBOSE,\n                                        children: \"Verbose\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleViewBadges,\n                                disabled: !debugState.enabled,\n                                style: {\n                                    flex: 1,\n                                    padding: '6px 12px',\n                                    backgroundColor: debugState.enabled ? '#3182CE' : '#2D3748',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    color: 'white',\n                                    cursor: debugState.enabled ? 'pointer' : 'not-allowed'\n                                },\n                                children: \"View Badges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearBadges,\n                                disabled: !debugState.enabled,\n                                style: {\n                                    flex: 1,\n                                    padding: '6px 12px',\n                                    backgroundColor: debugState.enabled ? '#E53E3E' : '#2D3748',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    color: 'white',\n                                    cursor: debugState.enabled ? 'pointer' : 'not-allowed'\n                                },\n                                children: \"Clear Badges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/DebugControls.tsx\n");

/***/ })

};
;