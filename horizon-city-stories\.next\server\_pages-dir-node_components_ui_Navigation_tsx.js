"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_ui_Navigation_tsx";
exports.ids = ["_pages-dir-node_components_ui_Navigation_tsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/ui/DesktopNav.tsx":
/*!**************************************!*\
  !*** ./components/ui/DesktopNav.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AppLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AppLink */ \"(pages-dir-node)/./components/ui/AppLink.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AppLink__WEBPACK_IMPORTED_MODULE_2__]);\n_AppLink__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n/**\n * Desktop navigation component\n */ const DesktopNav = ({ items })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"desktop-nav\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"flex gap-8\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: item.href,\n                        className: \"font-rajdhani font-medium text-lg transition-all\",\n                        style: {\n                            textShadow: item.style.textShadow,\n                            color: item.style.color,\n                            position: 'relative',\n                            padding: '0.25rem 0.5rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    position: 'absolute',\n                                    bottom: '-2px',\n                                    left: '0',\n                                    width: '100%',\n                                    height: '2px',\n                                    background: `linear-gradient(to right, transparent, ${item.style.borderColor || 'rgba(139, 92, 246, 0.7)'}, transparent)`,\n                                    transform: 'scaleX(0.7)',\n                                    opacity: '0.7',\n                                    transition: 'all 0.3s ease'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, undefined),\n                            item.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 13\n                    }, undefined)\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DesktopNav);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/DesktopNav.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/MobileMenu.tsx":
/*!**************************************!*\
  !*** ./components/ui/MobileMenu.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"react-dom\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _AppLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AppLink */ \"(pages-dir-node)/./components/ui/AppLink.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AppLink__WEBPACK_IMPORTED_MODULE_3__]);\n_AppLink__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n/**\n * Mobile menu component that renders directly into the body\n */ const MobileMenu = ({ isOpen, items, onNavClick })=>{\n    if (!isOpen || typeof document === 'undefined') return null;\n    // Render directly to body to avoid any container issues\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"mobile-menu-overlay\",\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 9000,\n            background: 'transparent',\n            pointerEvents: 'none',\n            overflow: 'hidden'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mobile-menu\",\n            style: {\n                position: 'fixed',\n                top: '64px',\n                right: '16px',\n                background: 'linear-gradient(to bottom, rgba(20, 10, 50, 0.98), rgba(13, 6, 32, 0.98))',\n                borderBottom: '1px solid rgba(76, 29, 149, 0.5)',\n                borderLeft: '1px solid rgba(76, 29, 149, 0.5)',\n                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.7)',\n                zIndex: 9999,\n                padding: '16px',\n                width: '250px',\n                maxHeight: 'calc(100vh - 80px)',\n                overflowY: 'auto',\n                overflowX: 'hidden',\n                transition: 'opacity 0.3s ease',\n                opacity: 1,\n                pointerEvents: 'auto',\n                borderRadius: '0 0 12px 12px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"flex flex-col gap-3 w-full\",\n                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"w-full text-right\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: item.href,\n                            className: \"font-rajdhani font-medium text-lg transition-all block py-2 px-4 w-full text-right\",\n                            style: {\n                                textShadow: item.style.textShadow,\n                                color: item.style.color,\n                                borderBottom: `1px solid ${item.style.borderColor || 'rgba(139, 92, 246, 0.3)'}`,\n                                textAlign: 'right',\n                                direction: 'rtl'\n                            },\n                            onClick: onNavClick,\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined), document.body);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileMenu);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/MobileMenu.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/MobileMenuButton.tsx":
/*!********************************************!*\
  !*** ./components/ui/MobileMenuButton.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * Hamburger button component for mobile navigation\n */ const MobileMenuButton = ({ isOpen, onClick })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"hamburger-btn\",\n        style: {\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: 'center',\n            gap: '6px',\n            backgroundColor: 'transparent',\n            border: 'none',\n            cursor: 'pointer',\n            zIndex: 50,\n            position: 'relative',\n            padding: '8px',\n            borderRadius: '4px',\n            background: isOpen ? 'rgba(76, 29, 149, 0.3)' : 'transparent',\n            transition: 'background 0.3s ease'\n        },\n        \"aria-label\": \"Toggle menu\",\n        \"aria-expanded\": isOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: isOpen ? '22px' : '24px',\n                    height: '2px',\n                    background: 'linear-gradient(to right, #22d3ee, #818cf8)',\n                    boxShadow: '0 0 5px rgba(139, 92, 246, 0.7)',\n                    transition: 'transform 0.3s ease, width 0.2s ease',\n                    transform: isOpen ? 'rotate(45deg) translate(5px, 5px)' : 'rotate(0)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '24px',\n                    height: '2px',\n                    background: 'linear-gradient(to right, #22d3ee, #818cf8)',\n                    boxShadow: '0 0 5px rgba(139, 92, 246, 0.7)',\n                    transition: 'opacity 0.2s ease, transform 0.2s ease',\n                    opacity: isOpen ? 0 : 1,\n                    transform: isOpen ? 'translateX(-5px)' : 'translateX(0)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: isOpen ? '22px' : '24px',\n                    height: '2px',\n                    background: 'linear-gradient(to right, #22d3ee, #818cf8)',\n                    boxShadow: '0 0 5px rgba(139, 92, 246, 0.7)',\n                    transition: 'transform 0.3s ease, width 0.2s ease',\n                    transform: isOpen ? 'rotate(-45deg) translate(5px, -5px)' : 'rotate(0)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileMenuButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/MobileMenuButton.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/NavItems.ts":
/*!***********************************!*\
  !*** ./components/ui/NavItems.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAV_ITEMS: () => (/* binding */ NAV_ITEMS)\n/* harmony export */ });\n/**\n * TypeScript interface for NavItem\n */ /**\n * Centralized navigation config\n */ const NAV_ITEMS = [\n    {\n        href: \"/blog\",\n        label: \"I HATE IT HERE\",\n        style: {\n            color: '#fef08a',\n            textShadow: '0 0 10px rgba(234, 179, 8, 1), 0 0 15px rgba(234, 179, 8, 0.8)',\n            borderColor: 'rgba(234, 179, 8, 0.3)'\n        }\n    },\n    {\n        href: \"/stories\",\n        label: \"STORIES\",\n        style: {\n            color: '#d6bcfa',\n            textShadow: '0 0 10px rgba(139, 92, 246, 1), 0 0 15px rgba(139, 92, 246, 0.8)',\n            borderColor: 'rgba(139, 92, 246, 0.3)'\n        }\n    },\n    {\n        href: \"/paydata\",\n        label: \"PAYDATA\",\n        style: {\n            color: '#d6bcfa',\n            textShadow: '0 0 10px rgba(139, 92, 246, 1), 0 0 15px rgba(139, 92, 246, 0.8)',\n            borderColor: 'rgba(139, 92, 246, 0.3)'\n        }\n    },\n    {\n        href: \"/outreach\",\n        label: \"OUTREACH\",\n        style: {\n            color: '#22d3ee',\n            textShadow: '0 0 10px rgba(34, 211, 238, 0.7)',\n            borderColor: 'rgba(34, 211, 238, 0.3)'\n        }\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/NavItems.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/Navigation.tsx":
/*!**************************************!*\
  !*** ./components/ui/Navigation.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _DesktopNav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DesktopNav */ \"(pages-dir-node)/./components/ui/DesktopNav.tsx\");\n/* harmony import */ var _MobileMenuButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MobileMenuButton */ \"(pages-dir-node)/./components/ui/MobileMenuButton.tsx\");\n/* harmony import */ var _MobileMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileMenu */ \"(pages-dir-node)/./components/ui/MobileMenu.tsx\");\n/* harmony import */ var _NavItems__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NavItems */ \"(pages-dir-node)/./components/ui/NavItems.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_DesktopNav__WEBPACK_IMPORTED_MODULE_2__, _MobileMenu__WEBPACK_IMPORTED_MODULE_4__]);\n([_DesktopNav__WEBPACK_IMPORTED_MODULE_2__, _MobileMenu__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n/**\n * Main Navigation component that handles responsive behavior\n */ const Navigation = ()=>{\n    // State for mobile menu\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State to track viewport width\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // State to track if we're on client side\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Define breakpoint\n    const mobileBreakpoint = 700;\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            // Mark as mounted to signal client-side rendering\n            setIsMounted(true);\n            // Only set window width on client-side\n            if (false) {}\n        }\n    }[\"Navigation.useEffect\"], []);\n    // Handle mobile menu toggle\n    const toggleMenu = ()=>{\n        const newIsOpen = !isMenuOpen;\n        setIsMenuOpen(newIsOpen);\n        // Toggle body class to prevent scrolling when menu is open\n        if (typeof document !== 'undefined') {\n            if (newIsOpen) {\n                document.body.classList.add('menu-open');\n            } else {\n                document.body.classList.remove('menu-open');\n            }\n        }\n    };\n    // Close menu when a link is clicked\n    const handleNavClick = ()=>{\n        setIsMenuOpen(false);\n        // Remove the menu-open class from body\n        if (typeof document !== 'undefined') {\n            document.body.classList.remove('menu-open');\n        }\n    };\n    // Determine if we're in mobile view\n    const isMobileView = windowWidth <= mobileBreakpoint;\n    // Only render on the client side\n    if (!isMounted) {\n        return null; // Return null during SSR to avoid hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            !isMobileView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DesktopNav__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                items: _NavItems__WEBPACK_IMPORTED_MODULE_5__.NAV_ITEMS\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                lineNumber: 89,\n                columnNumber: 25\n            }, undefined),\n            isMobileView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-nav-container\",\n                style: {\n                    position: 'relative',\n                    zIndex: 40\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileMenuButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        isOpen: isMenuOpen,\n                        onClick: toggleMenu\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileMenu__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isMenuOpen,\n                        items: _NavItems__WEBPACK_IMPORTED_MODULE_5__.NAV_ITEMS,\n                        onNavClick: handleNavClick\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/Navigation.tsx\n");

/***/ })

};
;