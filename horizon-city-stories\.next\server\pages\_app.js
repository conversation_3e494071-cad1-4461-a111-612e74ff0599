/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "(pages-dir-node)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_AppLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/AppLink */ \"(pages-dir-node)/./components/ui/AppLink.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__]);\n_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t border-purple-900 py-8 mt-12\",\n        style: {\n            borderColor: 'rgba(76, 29, 149, 0.5)',\n            background: 'linear-gradient(to bottom, rgba(13, 6, 32, 0.8), rgba(20, 10, 50, 0.9))',\n            boxShadow: 'inset 0 10px 20px rgba(76, 29, 149, 0.1)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center text-center mb-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-purple-400 font-rajdhani flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyan-400\",\n                                        children: \"[\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Horizon City\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyan-400\",\n                                        children: \"]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-5 w-full max-w-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                style: {\n                                    display: 'flex',\n                                    width: '100%',\n                                    justifyContent: 'space-between',\n                                    maxWidth: '500px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"text-gray-300 hover:text-purple-400 transition-all\",\n                                        style: {\n                                            display: 'inline-block',\n                                            padding: '6px 12px',\n                                            textDecoration: 'none',\n                                            borderRadius: '4px',\n                                            fontWeight: 500,\n                                            background: 'rgba(76, 29, 149, 0.1)',\n                                            border: '1px solid rgba(76, 29, 149, 0.2)'\n                                        },\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/blog\",\n                                        className: \"text-gray-300 hover:text-yellow-400 transition-all\",\n                                        style: {\n                                            display: 'inline-block',\n                                            padding: '6px 12px',\n                                            textDecoration: 'none',\n                                            borderRadius: '4px',\n                                            fontWeight: 500,\n                                            background: 'rgba(234, 179, 8, 0.1)',\n                                            border: '1px solid rgba(234, 179, 8, 0.2)'\n                                        },\n                                        children: \"I Hate It Here\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/stories\",\n                                        className: \"text-gray-300 hover:text-purple-400 transition-all\",\n                                        style: {\n                                            display: 'inline-block',\n                                            padding: '6px 12px',\n                                            textDecoration: 'none',\n                                            borderRadius: '4px',\n                                            fontWeight: 500,\n                                            background: 'rgba(76, 29, 149, 0.1)',\n                                            border: '1px solid rgba(76, 29, 149, 0.2)'\n                                        },\n                                        children: \"Stories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/paydata\",\n                                        className: \"text-gray-300 hover:text-purple-400 transition-all\",\n                                        style: {\n                                            display: 'inline-block',\n                                            padding: '6px 12px',\n                                            textDecoration: 'none',\n                                            borderRadius: '4px',\n                                            fontWeight: 500,\n                                            background: 'rgba(76, 29, 149, 0.1)',\n                                            border: '1px solid rgba(76, 29, 149, 0.2)'\n                                        },\n                                        children: \"Paydata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/outreach\",\n                                        className: \"text-gray-300 hover:text-purple-400 transition-all\",\n                                        style: {\n                                            display: 'inline-block',\n                                            padding: '6px 12px',\n                                            textDecoration: 'none',\n                                            borderRadius: '4px',\n                                            fontWeight: 500,\n                                            background: 'rgba(76, 29, 149, 0.1)',\n                                            border: '1px solid rgba(76, 29, 149, 0.2)'\n                                        },\n                                        children: \"Outreach\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"text-cyan-400 hover:text-cyan-300 transition-all text-lg font-medium\",\n                                style: {\n                                    textShadow: '0 0 10px rgba(56, 178, 172, 0.7)',\n                                    display: 'inline-block',\n                                    padding: '0.5rem 1.5rem',\n                                    border: '1px solid rgba(56, 178, 172, 0.3)',\n                                    borderRadius: '4px',\n                                    background: 'rgba(8, 60, 60, 0.2)'\n                                },\n                                children: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-px mb-4\",\n                            style: {\n                                background: 'linear-gradient(to right, transparent, rgba(139, 92, 246, 0.7), transparent)'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-center mb-1\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-sm text-center max-w-2xl\",\n                            children: \"Horizon City is a fictional cyberpunk universe. All content, characters, and artwork are protected under copyright law.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_AppLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/AppLink */ \"(pages-dir-node)/./components/ui/AppLink.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/breadcrumb-provider */ \"(pages-dir-node)/./components/ui/breadcrumb-provider.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__, _ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_4__]);\n([_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__, _ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n// Import the Navigation component with client-side only rendering\nconst Navigation = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-node_components_ui_Navigation_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../ui/Navigation */ \"(pages-dir-node)/./components/ui/Navigation.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\layout\\\\Header.tsx -> \" + \"../ui/Navigation\"\n        ]\n    },\n    ssr: false\n});\n// Import the debug display component\nconst DebugDisplay = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-node_components_layout_DebugDisplay_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./DebugDisplay */ \"(pages-dir-node)/./components/layout/DebugDisplay.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\layout\\\\Header.tsx -> \" + \"./DebugDisplay\"\n        ]\n    },\n    ssr: false\n});\nconst Header = ()=>{\n    const [showDebugTools, setShowDebugTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Add debug commands to window when on client\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"Header.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"border-b border-purple-900 sticky top-0 z-30 w-full overflow-visible\",\n        style: {\n            borderColor: 'rgba(76, 29, 149, 0.5)',\n            background: 'linear-gradient(to right, rgba(13, 6, 32, 0.9), rgba(20, 10, 50, 0.85))',\n            backdropFilter: 'blur(8px)',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto py-5 flex justify-between items-center relative\",\n                style: {\n                    width: '750px',\n                    maxWidth: '100%',\n                    position: 'relative',\n                    zIndex: 35\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold font-rajdhani tracking-wider\",\n                            style: {\n                                background: 'linear-gradient(to right, #c084fc, #818cf8)',\n                                WebkitBackgroundClip: 'text',\n                                WebkitTextFillColor: 'transparent',\n                                textShadow: '0 0 15px rgba(139, 92, 246, 0.5)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyan-400\",\n                                    style: {\n                                        WebkitTextFillColor: '#22d3ee',\n                                        textShadow: '0 0 10px rgba(34, 211, 238, 0.7)'\n                                    },\n                                    children: \"[\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"HORIZON CITY\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyan-400\",\n                                    style: {\n                                        WebkitTextFillColor: '#22d3ee',\n                                        textShadow: '0 0 10px rgba(34, 211, 238, 0.7)'\n                                    },\n                                    children: \"]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Navigation, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-2 border-t relative w-full\",\n                style: {\n                    borderColor: 'rgba(76, 29, 149, 0.3)',\n                    background: 'linear-gradient(to right, rgba(13, 6, 32, 0.7), rgba(20, 10, 50, 0.6))',\n                    zIndex: 20 // Lower z-index than the mobile menu\n                },\n                children: [\n                    showDebugTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugDisplay, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 28\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbDisplay, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/layout/Header.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/performance/IntelligentPagePrefetcher.tsx":
/*!**************************************************************!*\
  !*** ./components/performance/IntelligentPagePrefetcher.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useIntelligentPagePrefetching: () => (/* binding */ useIntelligentPagePrefetching)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\r\n * Navigation patterns - mapping of current pages to likely next pages\r\n * This helps with intelligent prefetching based on user journey patterns\r\n */ const NAVIGATION_PATTERNS = {\n    // Home page -> likely navigation targets\n    '/': [\n        '/stories',\n        '/characters',\n        '/locations',\n        '/about'\n    ],\n    // Stories main page -> likely navigation targets\n    '/stories': [\n        '/stories/featured',\n        '/stories/latest',\n        '/stories/series'\n    ],\n    // Characters page -> common character pages\n    '/characters': [],\n    // Series page -> first episodes\n    '/stories/series': [],\n    // Generic patterns - apply to many pages\n    '_story': [\n        '/characters',\n        '/locations',\n        '/stories'\n    ],\n    '_character': [\n        '/stories',\n        '/locations'\n    ]\n};\n/**\r\n * Determine if the current route matches a pattern key\r\n */ function matchesPatternKey(currentPath, patternKey) {\n    // Exact match\n    if (patternKey === currentPath) return true;\n    // Pattern keys starting with _ are generic patterns\n    if (patternKey.startsWith('_')) {\n        const pattern = patternKey.substring(1);\n        return currentPath.includes(pattern);\n    }\n    return false;\n}\n/**\r\n * Get pages to prefetch based on current path\r\n */ function getPagesToFetch(currentPath) {\n    const pagesToFetch = new Set();\n    // First check exact matches\n    if (NAVIGATION_PATTERNS[currentPath]) {\n        NAVIGATION_PATTERNS[currentPath].forEach((page)=>pagesToFetch.add(page));\n    }\n    // Then check pattern matches\n    Object.keys(NAVIGATION_PATTERNS).filter((key)=>key.startsWith('_')).forEach((patternKey)=>{\n        if (matchesPatternKey(currentPath, patternKey)) {\n            NAVIGATION_PATTERNS[patternKey].forEach((page)=>pagesToFetch.add(page));\n        }\n    });\n    return Array.from(pagesToFetch);\n}\n/**\r\n * Hook to automatically prefetch pages based on navigation patterns\r\n * \r\n * @example\r\n * ```tsx\r\n * // In _app.tsx or a layout component\r\n * useIntelligentPagePrefetching();\r\n * ```\r\n */ function useIntelligentPagePrefetching() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const [hasPrefetched, setHasPrefetched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useIntelligentPagePrefetching.useEffect\": ()=>{\n            if (true) return;\n            // Reset prefetch state when route changes\n            const handleRouteChange = {\n                \"useIntelligentPagePrefetching.useEffect.handleRouteChange\": ()=>{\n                    setHasPrefetched(false);\n                }\n            }[\"useIntelligentPagePrefetching.useEffect.handleRouteChange\"];\n            router.events.on('routeChangeComplete', handleRouteChange);\n            // Prefetch pages after a short delay\n            const timer = setTimeout({\n                \"useIntelligentPagePrefetching.useEffect.timer\": ()=>{\n                    const pagesToFetch = getPagesToFetch(router.pathname);\n                    // First batch - high priority (immediate likely navigation)\n                    const highPriorityPages = pagesToFetch.slice(0, 2);\n                    highPriorityPages.forEach({\n                        \"useIntelligentPagePrefetching.useEffect.timer\": (page)=>{\n                            router.prefetch(page);\n                        }\n                    }[\"useIntelligentPagePrefetching.useEffect.timer\"]);\n                    // Second batch - normal priority (after a delay)\n                    setTimeout({\n                        \"useIntelligentPagePrefetching.useEffect.timer\": ()=>{\n                            const normalPriorityPages = pagesToFetch.slice(2);\n                            normalPriorityPages.forEach({\n                                \"useIntelligentPagePrefetching.useEffect.timer\": (page)=>{\n                                    router.prefetch(page);\n                                }\n                            }[\"useIntelligentPagePrefetching.useEffect.timer\"]);\n                            setHasPrefetched(true);\n                            console.log(`🔄 Prefetched ${pagesToFetch.length} pages based on current route: ${router.pathname}`);\n                        }\n                    }[\"useIntelligentPagePrefetching.useEffect.timer\"], 1500);\n                }\n            }[\"useIntelligentPagePrefetching.useEffect.timer\"], 500);\n            return ({\n                \"useIntelligentPagePrefetching.useEffect\": ()=>{\n                    clearTimeout(timer);\n                    router.events.off('routeChangeComplete', handleRouteChange);\n                }\n            })[\"useIntelligentPagePrefetching.useEffect\"];\n        }\n    }[\"useIntelligentPagePrefetching.useEffect\"], [\n        router,\n        router.pathname,\n        hasPrefetched\n    ]);\n}\n/**\r\n * Component that implements intelligent page prefetching\r\n */ const IntelligentPagePrefetcher = ()=>{\n    useIntelligentPagePrefetching();\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IntelligentPagePrefetcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/performance/IntelligentPagePrefetcher.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/AppLink.tsx":
/*!***********************************!*\
  !*** ./components/ui/AppLink.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _utils_access_badges__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/access-badges */ \"(pages-dir-node)/./utils/access-badges.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, _utils_access_badges__WEBPACK_IMPORTED_MODULE_4__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, _utils_access_badges__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n// We'll dynamically import the router when needed\n\n/**\n * An enhanced link component that combines Next.js Link with Chakra UI Link\n * Features:\n * - Proper handling of navigation\n * - Support for delayed badge processing\n * - Preloading has been disabled as requested\n */ const AppLink = ({ href, children, className, prefetchPriority: _prefetchPriority = 'high', preloadResources: _preloadResources = true, preconnect: _preconnect = true, preloadImages: _preloadImages = true, instantNavigation: _instantNavigation = false, ...chakraProps })=>{\n    const isExternal = href.startsWith('http') || href.startsWith('//');\n    // We no longer need hover-based preloading as we're preloading all links on page load\n    // This is kept for backward compatibility but doesn't do anything\n    const _preloadHandlers = {};\n    // Handle click to ensure proper navigation\n    const handleClick = (e)=>{\n        // If it's an external link, let the browser handle it\n        if (isExternal) return;\n        // If the user is pressing modifier keys (new tab, etc.), let the browser handle it\n        if (e.metaKey || e.ctrlKey || e.shiftKey) return;\n        e.preventDefault();\n        // Check if we're navigating away from a story page\n        const isLeavingStoryPage =  false && 0;\n        // If we're leaving a story page, process any delayed badges\n        if (isLeavingStoryPage) {\n            (0,_utils_access_badges__WEBPACK_IMPORTED_MODULE_4__.processDelayedBadges)();\n        }\n        // Check if we're trying to navigate to the current URL\n        if (false) {}\n        // Check if the loading screen is visible\n        if (false) {}\n        // Hide any mobile menus that might be open\n        if (false) {}\n        // First, trigger content fade-out\n        if (false) {}\n        // Use Next.js router for navigation after a short delay to allow fade-out\n        setTimeout(()=>{\n            Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\", 23)).then(({ default: router })=>{\n                console.log(`🔄 [AppLink] Navigating to: ${href}`);\n                router.push(href);\n            });\n        }, 300); // Short delay for content fade-out\n    };\n    // Create NextLink props without prefetch\n    const nextLinkProps = {\n        href,\n        prefetch: false // Disable prefetching as requested\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Link, {\n        as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n        ...nextLinkProps,\n        className: className,\n        onClick: handleClick,\n        ..._preloadHandlers,\n        ...chakraProps,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\AppLink.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppLink);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvdWkvQXBwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUNPO0FBQ21EO0FBQ3BGLGtEQUFrRDtBQUNlO0FBYWpFOzs7Ozs7Q0FNQyxHQUNELE1BQU1LLFVBQWtDLENBQUMsRUFDdkNDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLGtCQUFrQkMsb0JBQW9CLE1BQU0sRUFDNUNDLGtCQUFrQkMsb0JBQW9CLElBQUksRUFDMUNDLFlBQVlDLGNBQWMsSUFBSSxFQUM5QkMsZUFBZUMsaUJBQWlCLElBQUksRUFDcENDLG1CQUFtQkMscUJBQXFCLEtBQUssRUFDN0MsR0FBR0MsYUFDSjtJQUNDLE1BQU1DLGFBQWFkLEtBQUtlLFVBQVUsQ0FBQyxXQUFXZixLQUFLZSxVQUFVLENBQUM7SUFFOUQsc0ZBQXNGO0lBQ3RGLGtFQUFrRTtJQUNsRSxNQUFNQyxtQkFBbUIsQ0FBQztJQUUxQiwyQ0FBMkM7SUFDM0MsTUFBTUMsY0FBYyxDQUFDQztRQUNuQixzREFBc0Q7UUFDdEQsSUFBSUosWUFBWTtRQUVoQixtRkFBbUY7UUFDbkYsSUFBSUksRUFBRUMsT0FBTyxJQUFJRCxFQUFFRSxPQUFPLElBQUlGLEVBQUVHLFFBQVEsRUFBRTtRQUUxQ0gsRUFBRUksY0FBYztRQUVoQixtREFBbUQ7UUFDbkQsTUFBTUMscUJBQXFCLE1BQTZCLElBQ3REQyxDQUE4QztRQUVoRCw0REFBNEQ7UUFDNUQsSUFBSUQsb0JBQW9CO1lBQ3RCekIsMEVBQW9CQTtRQUN0QjtRQUVBLHVEQUF1RDtRQUN2RCxJQUFJLEtBQWtFRSxFQUFFLEVBR3ZFO1FBRUQseUNBQXlDO1FBQ3pDLElBQUksS0FBNkIsRUFBRSxFQW1CbEM7UUFFRCwyQ0FBMkM7UUFDM0MsSUFBSSxLQUE2QixFQUFFLEVBSWxDO1FBRUQsa0NBQWtDO1FBQ2xDLElBQUksS0FBNkIsRUFBRSxFQVFsQztRQUVELDBFQUEwRTtRQUMxRWlDLFdBQVc7WUFDVCwrSkFBcUIsQ0FBQ0MsSUFBSSxDQUFDLENBQUMsRUFBRUMsU0FBU0MsTUFBTSxFQUFFO2dCQUM3Q1IsUUFBUUMsR0FBRyxDQUFDLENBQUMsNEJBQTRCLEVBQUU3QixNQUFNO2dCQUNqRG9DLE9BQU9DLElBQUksQ0FBQ3JDO1lBQ2Q7UUFDRixHQUFHLE1BQU0sbUNBQW1DO0lBQzlDO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU00QyxnQkFBZ0I7UUFDcEI1QztRQUNBNkMsVUFBVSxNQUFNLG1DQUFtQztJQUNyRDtJQUVBLHFCQUNFLDhEQUFDaEQsa0RBQVVBO1FBQ1RpRCxJQUFJbkQsa0RBQVFBO1FBQ1gsR0FBR2lELGFBQWE7UUFDakIxQyxXQUFXQTtRQUNYNkMsU0FBUzlCO1FBQ1IsR0FBR0QsZ0JBQWdCO1FBQ25CLEdBQUdILFdBQVc7a0JBRWRaOzs7Ozs7QUFHUDtBQUVBLGlFQUFlRixPQUFPQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFRcXFByb2plY3RzXFxob3Jpem9uLWNpdHktc3Rvcmllc1xcY29tcG9uZW50c1xcdWlcXEFwcExpbmsudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTmV4dExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IExpbmsgYXMgQ2hha3JhTGluaywgTGlua1Byb3BzIGFzIENoYWtyYUxpbmtQcm9wcyB9IGZyb20gJ0BjaGFrcmEtdWkvcmVhY3QnO1xuLy8gV2UnbGwgZHluYW1pY2FsbHkgaW1wb3J0IHRoZSByb3V0ZXIgd2hlbiBuZWVkZWRcbmltcG9ydCB7IHByb2Nlc3NEZWxheWVkQmFkZ2VzIH0gZnJvbSAnLi4vLi4vdXRpbHMvYWNjZXNzLWJhZGdlcyc7XG5cbmludGVyZmFjZSBBcHBMaW5rUHJvcHMgZXh0ZW5kcyBDaGFrcmFMaW5rUHJvcHMge1xuICBocmVmOiBzdHJpbmc7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgcHJlZmV0Y2hQcmlvcml0eT86ICdoaWdoJyB8ICdsb3cnIHwgJ2F1dG8nOyAvLyBLZXB0IGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5IGJ1dCBub3QgdXNlZFxuICBwcmVsb2FkUmVzb3VyY2VzPzogYm9vbGVhbjsgLy8gS2VwdCBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eSBidXQgbm90IHVzZWRcbiAgcHJlY29ubmVjdD86IGJvb2xlYW47IC8vIEtlcHQgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHkgYnV0IG5vdCB1c2VkXG4gIHByZWxvYWRJbWFnZXM/OiBib29sZWFuOyAvLyBLZXB0IGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5IGJ1dCBub3QgdXNlZFxuICBpbnN0YW50TmF2aWdhdGlvbj86IGJvb2xlYW47XG59XG5cbi8qKlxuICogQW4gZW5oYW5jZWQgbGluayBjb21wb25lbnQgdGhhdCBjb21iaW5lcyBOZXh0LmpzIExpbmsgd2l0aCBDaGFrcmEgVUkgTGlua1xuICogRmVhdHVyZXM6XG4gKiAtIFByb3BlciBoYW5kbGluZyBvZiBuYXZpZ2F0aW9uXG4gKiAtIFN1cHBvcnQgZm9yIGRlbGF5ZWQgYmFkZ2UgcHJvY2Vzc2luZ1xuICogLSBQcmVsb2FkaW5nIGhhcyBiZWVuIGRpc2FibGVkIGFzIHJlcXVlc3RlZFxuICovXG5jb25zdCBBcHBMaW5rOiBSZWFjdC5GQzxBcHBMaW5rUHJvcHM+ID0gKHtcbiAgaHJlZixcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbiAgcHJlZmV0Y2hQcmlvcml0eTogX3ByZWZldGNoUHJpb3JpdHkgPSAnaGlnaCcsXG4gIHByZWxvYWRSZXNvdXJjZXM6IF9wcmVsb2FkUmVzb3VyY2VzID0gdHJ1ZSxcbiAgcHJlY29ubmVjdDogX3ByZWNvbm5lY3QgPSB0cnVlLFxuICBwcmVsb2FkSW1hZ2VzOiBfcHJlbG9hZEltYWdlcyA9IHRydWUsXG4gIGluc3RhbnROYXZpZ2F0aW9uOiBfaW5zdGFudE5hdmlnYXRpb24gPSBmYWxzZSxcbiAgLi4uY2hha3JhUHJvcHNcbn0pID0+IHtcbiAgY29uc3QgaXNFeHRlcm5hbCA9IGhyZWYuc3RhcnRzV2l0aCgnaHR0cCcpIHx8IGhyZWYuc3RhcnRzV2l0aCgnLy8nKTtcblxuICAvLyBXZSBubyBsb25nZXIgbmVlZCBob3Zlci1iYXNlZCBwcmVsb2FkaW5nIGFzIHdlJ3JlIHByZWxvYWRpbmcgYWxsIGxpbmtzIG9uIHBhZ2UgbG9hZFxuICAvLyBUaGlzIGlzIGtlcHQgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHkgYnV0IGRvZXNuJ3QgZG8gYW55dGhpbmdcbiAgY29uc3QgX3ByZWxvYWRIYW5kbGVycyA9IHt9O1xuXG4gIC8vIEhhbmRsZSBjbGljayB0byBlbnN1cmUgcHJvcGVyIG5hdmlnYXRpb25cbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoZTogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIC8vIElmIGl0J3MgYW4gZXh0ZXJuYWwgbGluaywgbGV0IHRoZSBicm93c2VyIGhhbmRsZSBpdFxuICAgIGlmIChpc0V4dGVybmFsKSByZXR1cm47XG5cbiAgICAvLyBJZiB0aGUgdXNlciBpcyBwcmVzc2luZyBtb2RpZmllciBrZXlzIChuZXcgdGFiLCBldGMuKSwgbGV0IHRoZSBicm93c2VyIGhhbmRsZSBpdFxuICAgIGlmIChlLm1ldGFLZXkgfHwgZS5jdHJsS2V5IHx8IGUuc2hpZnRLZXkpIHJldHVybjtcblxuICAgIGUucHJldmVudERlZmF1bHQoKTtcblxuICAgIC8vIENoZWNrIGlmIHdlJ3JlIG5hdmlnYXRpbmcgYXdheSBmcm9tIGEgc3RvcnkgcGFnZVxuICAgIGNvbnN0IGlzTGVhdmluZ1N0b3J5UGFnZSA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmXG4gICAgICB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUuaW5jbHVkZXMoJy9zdG9yaWVzLycpO1xuXG4gICAgLy8gSWYgd2UncmUgbGVhdmluZyBhIHN0b3J5IHBhZ2UsIHByb2Nlc3MgYW55IGRlbGF5ZWQgYmFkZ2VzXG4gICAgaWYgKGlzTGVhdmluZ1N0b3J5UGFnZSkge1xuICAgICAgcHJvY2Vzc0RlbGF5ZWRCYWRnZXMoKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB3ZSdyZSB0cnlpbmcgdG8gbmF2aWdhdGUgdG8gdGhlIGN1cnJlbnQgVVJMXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gaHJlZikge1xuICAgICAgY29uc29sZS5sb2coYPCfmqsgW0FwcExpbmtdIFByZXZlbnRlZCBuYXZpZ2F0aW9uIHRvIHNhbWUgVVJMOiAke2hyZWZ9YCk7XG4gICAgICByZXR1cm47IC8vIFByZXZlbnQgbmF2aWdhdGlvbiB0byB0aGUgc2FtZSBVUkxcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB0aGUgbG9hZGluZyBzY3JlZW4gaXMgdmlzaWJsZVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgbG9hZGluZ1NjcmVlbiA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJ1tkYXRhLWxvYWRpbmctc2NyZWVuPVwidHJ1ZVwiXScpO1xuICAgICAgaWYgKGxvYWRpbmdTY3JlZW4pIHtcbiAgICAgICAgY29uc29sZS5sb2coYOKPsyBbQXBwTGlua10gTG9hZGluZyBzY3JlZW4gaXMgdmlzaWJsZSwgZGVsYXlpbmcgbmF2aWdhdGlvbiB0bzogJHtocmVmfWApO1xuICAgICAgICAvLyBXYWl0IGZvciB0aGUgbG9hZGluZyBzY3JlZW4gdG8gYmUgaGlkZGVuIGJlZm9yZSBuYXZpZ2F0aW5nXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIC8vIENoZWNrIGFnYWluIGlmIHdlJ3JlIHN0aWxsIG9uIHRoZSBzYW1lIHBhZ2VcbiAgICAgICAgICBpZiAod2luZG93LmxvY2F0aW9uLnBhdGhuYW1lID09PSBocmVmKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+aqyBbQXBwTGlua10gU3RpbGwgb24gc2FtZSBVUkwsIGNhbmNlbGluZyBkZWxheWVkIG5hdmlnYXRpb24gdG86ICR7aHJlZn1gKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgICAgY29uc29sZS5sb2coYPCflIQgW0FwcExpbmtdIE5vdyBuYXZpZ2F0aW5nIHRvOiAke2hyZWZ9IGFmdGVyIGxvYWRpbmcgc2NyZWVuIGRlbGF5YCk7XG4gICAgICAgICAgLy8gVXNlIE5leHQuanMgcm91dGVyIGZvciBuYXZpZ2F0aW9uXG4gICAgICAgICAgaW1wb3J0KCduZXh0L3JvdXRlcicpLnRoZW4oKHsgZGVmYXVsdDogcm91dGVyIH0pID0+IHtcbiAgICAgICAgICAgIHJvdXRlci5wdXNoKGhyZWYpO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9LCA1MDApO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gSGlkZSBhbnkgbW9iaWxlIG1lbnVzIHRoYXQgbWlnaHQgYmUgb3BlblxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgLy8gRGlzcGF0Y2ggYSBjdXN0b20gZXZlbnQgdG8gbm90aWZ5IF9hcHAudHN4IHRvIGNsb3NlIHRoZSBtb2JpbGUgbWVudVxuICAgICAgY29uc3QgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoJ2Nsb3NlTW9iaWxlTWVudScpO1xuICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xuICAgIH1cblxuICAgIC8vIEZpcnN0LCB0cmlnZ2VyIGNvbnRlbnQgZmFkZS1vdXRcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIC8vIEZpbmQgdGhlIG1haW4gY29udGVudCBjb250YWluZXJcbiAgICAgIGNvbnN0IG1haW5Db250ZW50ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLm1pbi1oLXNjcmVlbi5jeWJlcnB1bmstYmcnKTtcbiAgICAgIGlmIChtYWluQ29udGVudCkge1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+UhCBbQXBwTGlua10gRmFkaW5nIG91dCBjb250ZW50IGJlZm9yZSBuYXZpZ2F0aW5nIHRvOiAke2hyZWZ9YCk7XG4gICAgICAgIC8vIEFwcGx5IGZhZGUtb3V0IGVmZmVjdFxuICAgICAgICAobWFpbkNvbnRlbnQgYXMgSFRNTEVsZW1lbnQpLnN0eWxlLm9wYWNpdHkgPSAnMCc7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gVXNlIE5leHQuanMgcm91dGVyIGZvciBuYXZpZ2F0aW9uIGFmdGVyIGEgc2hvcnQgZGVsYXkgdG8gYWxsb3cgZmFkZS1vdXRcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGltcG9ydCgnbmV4dC9yb3V0ZXInKS50aGVuKCh7IGRlZmF1bHQ6IHJvdXRlciB9KSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFtBcHBMaW5rXSBOYXZpZ2F0aW5nIHRvOiAke2hyZWZ9YCk7XG4gICAgICAgIHJvdXRlci5wdXNoKGhyZWYpO1xuICAgICAgfSk7XG4gICAgfSwgMzAwKTsgLy8gU2hvcnQgZGVsYXkgZm9yIGNvbnRlbnQgZmFkZS1vdXRcbiAgfTtcblxuICAvLyBDcmVhdGUgTmV4dExpbmsgcHJvcHMgd2l0aG91dCBwcmVmZXRjaFxuICBjb25zdCBuZXh0TGlua1Byb3BzID0ge1xuICAgIGhyZWYsXG4gICAgcHJlZmV0Y2g6IGZhbHNlIC8vIERpc2FibGUgcHJlZmV0Y2hpbmcgYXMgcmVxdWVzdGVkXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Q2hha3JhTGlua1xuICAgICAgYXM9e05leHRMaW5rfVxuICAgICAgey4uLm5leHRMaW5rUHJvcHN9XG4gICAgICBjbGFzc05hbWU9e2NsYXNzTmFtZX1cbiAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxuICAgICAgey4uLl9wcmVsb2FkSGFuZGxlcnN9XG4gICAgICB7Li4uY2hha3JhUHJvcHN9XG4gICAgPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQ2hha3JhTGluaz5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEFwcExpbms7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJOZXh0TGluayIsIkxpbmsiLCJDaGFrcmFMaW5rIiwicHJvY2Vzc0RlbGF5ZWRCYWRnZXMiLCJBcHBMaW5rIiwiaHJlZiIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwicHJlZmV0Y2hQcmlvcml0eSIsIl9wcmVmZXRjaFByaW9yaXR5IiwicHJlbG9hZFJlc291cmNlcyIsIl9wcmVsb2FkUmVzb3VyY2VzIiwicHJlY29ubmVjdCIsIl9wcmVjb25uZWN0IiwicHJlbG9hZEltYWdlcyIsIl9wcmVsb2FkSW1hZ2VzIiwiaW5zdGFudE5hdmlnYXRpb24iLCJfaW5zdGFudE5hdmlnYXRpb24iLCJjaGFrcmFQcm9wcyIsImlzRXh0ZXJuYWwiLCJzdGFydHNXaXRoIiwiX3ByZWxvYWRIYW5kbGVycyIsImhhbmRsZUNsaWNrIiwiZSIsIm1ldGFLZXkiLCJjdHJsS2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImlzTGVhdmluZ1N0b3J5UGFnZSIsIndpbmRvdyIsImxvY2F0aW9uIiwicGF0aG5hbWUiLCJpbmNsdWRlcyIsImNvbnNvbGUiLCJsb2ciLCJsb2FkaW5nU2NyZWVuIiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwic2V0VGltZW91dCIsInRoZW4iLCJkZWZhdWx0Iiwicm91dGVyIiwicHVzaCIsImV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJkaXNwYXRjaEV2ZW50IiwibWFpbkNvbnRlbnQiLCJzdHlsZSIsIm9wYWNpdHkiLCJuZXh0TGlua1Byb3BzIiwicHJlZmV0Y2giLCJhcyIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/AppLink.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/RestrictionProvider.tsx":
/*!***********************************************!*\
  !*** ./components/ui/RestrictionProvider.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_restriction_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/restriction-manager */ \"(pages-dir-node)/./utils/restriction-manager.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_restriction_manager__WEBPACK_IMPORTED_MODULE_2__]);\n_utils_restriction_manager__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/**\n * RestrictionProvider Component\n *\n * A component that initializes the restriction system and provides\n * context for restriction-related functionality.\n */ \n\n\nconst RestrictionProvider = ({ children })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RestrictionProvider.useEffect\": ()=>{\n            // Initialize the restriction system after all images are loaded\n            if (false) {} else {\n                // Fallback to immediate initialization if tracker is not available\n                (0,_utils_restriction_manager__WEBPACK_IMPORTED_MODULE_2__.initializeOnLoad)();\n            }\n        }\n    }[\"RestrictionProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RestrictionProvider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvdWkvUmVzdHJpY3Rpb25Qcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7O0NBS0M7QUFFd0M7QUFDMEI7QUFNbkUsTUFBTUcsc0JBQTBELENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzNFSCxnREFBU0E7eUNBQUM7WUFDUixnRUFBZ0U7WUFDaEUsSUFBSSxLQUEyRCxFQUFFLEVBTWhFLE1BQU07Z0JBQ0wsbUVBQW1FO2dCQUNuRUMsNEVBQWdCQTtZQUNsQjtRQUNGO3dDQUFHLEVBQUU7SUFFTCxxQkFBTztrQkFBR0U7O0FBQ1o7QUFFQSxpRUFBZUQsbUJBQW1CQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFRcXFByb2plY3RzXFxob3Jpem9uLWNpdHktc3Rvcmllc1xcY29tcG9uZW50c1xcdWlcXFJlc3RyaWN0aW9uUHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmVzdHJpY3Rpb25Qcm92aWRlciBDb21wb25lbnRcbiAqXG4gKiBBIGNvbXBvbmVudCB0aGF0IGluaXRpYWxpemVzIHRoZSByZXN0cmljdGlvbiBzeXN0ZW0gYW5kIHByb3ZpZGVzXG4gKiBjb250ZXh0IGZvciByZXN0cmljdGlvbi1yZWxhdGVkIGZ1bmN0aW9uYWxpdHkuXG4gKi9cblxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGluaXRpYWxpemVPbkxvYWQgfSBmcm9tICcuLi8uLi91dGlscy9yZXN0cmljdGlvbi1tYW5hZ2VyJztcblxuaW50ZXJmYWNlIFJlc3RyaWN0aW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmNvbnN0IFJlc3RyaWN0aW9uUHJvdmlkZXI6IFJlYWN0LkZDPFJlc3RyaWN0aW9uUHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbGl6ZSB0aGUgcmVzdHJpY3Rpb24gc3lzdGVtIGFmdGVyIGFsbCBpbWFnZXMgYXJlIGxvYWRlZFxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cucmF3SW1hZ2VMb2FkVHJhY2tlcikge1xuICAgICAgLy8gVXNlIHRoZSBnbG9iYWwgaW1hZ2UgbG9hZCB0cmFja2VyIHRvIHdhaXQgZm9yIGFsbCBpbWFnZXNcbiAgICAgIHdpbmRvdy5yYXdJbWFnZUxvYWRUcmFja2VyLm9uQWxsSW1hZ2VzTG9hZGVkKCgpID0+IHtcbiAgICAgICAgLy8gSW5pdGlhbGl6ZSB0aGUgcmVzdHJpY3Rpb24gc3lzdGVtIGFmdGVyIGltYWdlcyBhcmUgbG9hZGVkXG4gICAgICAgIGluaXRpYWxpemVPbkxvYWQoKTtcbiAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBGYWxsYmFjayB0byBpbW1lZGlhdGUgaW5pdGlhbGl6YXRpb24gaWYgdHJhY2tlciBpcyBub3QgYXZhaWxhYmxlXG4gICAgICBpbml0aWFsaXplT25Mb2FkKCk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPjtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFJlc3RyaWN0aW9uUHJvdmlkZXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJpbml0aWFsaXplT25Mb2FkIiwiUmVzdHJpY3Rpb25Qcm92aWRlciIsImNoaWxkcmVuIiwid2luZG93IiwicmF3SW1hZ2VMb2FkVHJhY2tlciIsIm9uQWxsSW1hZ2VzTG9hZGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/RestrictionProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/breadcrumb-provider.tsx":
/*!***********************************************!*\
  !*** ./components/ui/breadcrumb-provider.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BreadcrumbDisplay: () => (/* binding */ BreadcrumbDisplay),\n/* harmony export */   BreadcrumbProvider: () => (/* binding */ BreadcrumbProvider),\n/* harmony export */   useBreadcrumb: () => (/* binding */ useBreadcrumb)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./breadcrumb */ \"(pages-dir-node)/./components/ui/breadcrumb.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_breadcrumb__WEBPACK_IMPORTED_MODULE_2__]);\n_breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Create the context with default values\nconst BreadcrumbContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    items: [],\n    setBreadcrumb: ()=>{}\n});\n// Hook to use the breadcrumb context\nconst useBreadcrumb = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BreadcrumbContext);\nconst BreadcrumbProvider = ({ children })=>{\n    // Use state instead of ref to ensure re-renders when breadcrumbs change\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Use useCallback to memoize the setBreadcrumb function\n    const setBreadcrumb = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"BreadcrumbProvider.useCallback[setBreadcrumb]\": (newItems)=>{\n            // Update the state to trigger re-renders\n            setItems(newItems);\n        }\n    }[\"BreadcrumbProvider.useCallback[setBreadcrumb]\"], []);\n    // We don't need to listen for URL changes anymore\n    // Each page will set its own breadcrumb\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreadcrumbContext.Provider, {\n        value: {\n            items,\n            setBreadcrumb\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb-provider.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n// Component to render the breadcrumb\nconst BreadcrumbDisplay = ()=>{\n    const { items } = useBreadcrumb();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Only render on client side\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BreadcrumbDisplay.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"BreadcrumbDisplay.useEffect\"], []);\n    if (!mounted || !items || items.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: '750px',\n            width: '750px',\n            margin: '0 auto',\n            textAlign: 'left'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_breadcrumb__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            items: items\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb-provider.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb-provider.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvdWkvYnJlYWRjcnVtYi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXlGO0FBQ25EO0FBZXRDLHlDQUF5QztBQUN6QyxNQUFNTSxrQ0FBb0JMLG9EQUFhQSxDQUF3QjtJQUM3RE0sT0FBTyxFQUFFO0lBQ1RDLGVBQWUsS0FBTztBQUN4QjtBQUVBLHFDQUFxQztBQUM5QixNQUFNQyxnQkFBZ0IsSUFBTVAsaURBQVVBLENBQUNJLG1CQUFtQjtBQU8xRCxNQUFNSSxxQkFBd0QsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDaEYsd0VBQXdFO0lBQ3hFLE1BQU0sQ0FBQ0osT0FBT0ssU0FBUyxHQUFHVCwrQ0FBUUEsQ0FBbUIsRUFBRTtJQUV2RCx3REFBd0Q7SUFDeEQsTUFBTUssZ0JBQWdCUix3REFBaUI7eURBQUMsQ0FBQ2M7WUFDdkMseUNBQXlDO1lBQ3pDRixTQUFTRTtRQUNYO3dEQUFHLEVBQUU7SUFFTCxrREFBa0Q7SUFDbEQsd0NBQXdDO0lBRXhDLHFCQUNFLDhEQUFDUixrQkFBa0JTLFFBQVE7UUFBQ0MsT0FBTztZQUFFVDtZQUFPQztRQUFjO2tCQUN2REc7Ozs7OztBQUdQLEVBQUU7QUFFRixxQ0FBcUM7QUFDOUIsTUFBTU0sb0JBQThCO0lBQ3pDLE1BQU0sRUFBRVYsS0FBSyxFQUFFLEdBQUdFO0lBQ2xCLE1BQU0sQ0FBQ1MsU0FBU0MsV0FBVyxHQUFHaEIsK0NBQVFBLENBQUM7SUFFdkMsNkJBQTZCO0lBQzdCQyxnREFBU0E7dUNBQUM7WUFDUmUsV0FBVztRQUNiO3NDQUFHLEVBQUU7SUFFTCxJQUFJLENBQUNELFdBQVcsQ0FBQ1gsU0FBU0EsTUFBTWEsTUFBTSxLQUFLLEdBQUc7UUFDNUMsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU87WUFBRUMsVUFBVTtZQUFTQyxPQUFPO1lBQVNDLFFBQVE7WUFBVUMsV0FBVztRQUFPO2tCQUNuRiw0RUFBQ3JCLG1EQUFVQTtZQUFDRSxPQUFPQTs7Ozs7Ozs7Ozs7QUFHekIsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxUXFxQcm9qZWN0c1xcaG9yaXpvbi1jaXR5LXN0b3JpZXNcXGNvbXBvbmVudHNcXHVpXFxicmVhZGNydW1iLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEJyZWFkY3J1bWIgZnJvbSAnLi9icmVhZGNydW1iJztcblxuLy8gRGVmaW5lIHRoZSBzaGFwZSBvZiBvdXIgYnJlYWRjcnVtYiBpdGVtc1xuZXhwb3J0IGludGVyZmFjZSBCcmVhZGNydW1iSXRlbSB7XG4gIGxhYmVsOiBzdHJpbmc7XG4gIGhyZWY/OiBzdHJpbmc7XG4gIGlzQ3VycmVudFBhZ2U/OiBib29sZWFuO1xufVxuXG4vLyBEZWZpbmUgdGhlIGNvbnRleHQgc2hhcGVcbmludGVyZmFjZSBCcmVhZGNydW1iQ29udGV4dFR5cGUge1xuICBpdGVtczogQnJlYWRjcnVtYkl0ZW1bXTtcbiAgc2V0QnJlYWRjcnVtYjogKGl0ZW1zOiBCcmVhZGNydW1iSXRlbVtdKSA9PiB2b2lkO1xufVxuXG4vLyBDcmVhdGUgdGhlIGNvbnRleHQgd2l0aCBkZWZhdWx0IHZhbHVlc1xuY29uc3QgQnJlYWRjcnVtYkNvbnRleHQgPSBjcmVhdGVDb250ZXh0PEJyZWFkY3J1bWJDb250ZXh0VHlwZT4oe1xuICBpdGVtczogW10sXG4gIHNldEJyZWFkY3J1bWI6ICgpID0+IHt9LFxufSk7XG5cbi8vIEhvb2sgdG8gdXNlIHRoZSBicmVhZGNydW1iIGNvbnRleHRcbmV4cG9ydCBjb25zdCB1c2VCcmVhZGNydW1iID0gKCkgPT4gdXNlQ29udGV4dChCcmVhZGNydW1iQ29udGV4dCk7XG5cbi8vIFByb3ZpZGVyIGNvbXBvbmVudFxuaW50ZXJmYWNlIEJyZWFkY3J1bWJQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEJyZWFkY3J1bWJQcm92aWRlcjogUmVhY3QuRkM8QnJlYWRjcnVtYlByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICAvLyBVc2Ugc3RhdGUgaW5zdGVhZCBvZiByZWYgdG8gZW5zdXJlIHJlLXJlbmRlcnMgd2hlbiBicmVhZGNydW1icyBjaGFuZ2VcbiAgY29uc3QgW2l0ZW1zLCBzZXRJdGVtc10gPSB1c2VTdGF0ZTxCcmVhZGNydW1iSXRlbVtdPihbXSk7XG5cbiAgLy8gVXNlIHVzZUNhbGxiYWNrIHRvIG1lbW9pemUgdGhlIHNldEJyZWFkY3J1bWIgZnVuY3Rpb25cbiAgY29uc3Qgc2V0QnJlYWRjcnVtYiA9IFJlYWN0LnVzZUNhbGxiYWNrKChuZXdJdGVtczogQnJlYWRjcnVtYkl0ZW1bXSkgPT4ge1xuICAgIC8vIFVwZGF0ZSB0aGUgc3RhdGUgdG8gdHJpZ2dlciByZS1yZW5kZXJzXG4gICAgc2V0SXRlbXMobmV3SXRlbXMpO1xuICB9LCBbXSk7XG5cbiAgLy8gV2UgZG9uJ3QgbmVlZCB0byBsaXN0ZW4gZm9yIFVSTCBjaGFuZ2VzIGFueW1vcmVcbiAgLy8gRWFjaCBwYWdlIHdpbGwgc2V0IGl0cyBvd24gYnJlYWRjcnVtYlxuXG4gIHJldHVybiAoXG4gICAgPEJyZWFkY3J1bWJDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IGl0ZW1zLCBzZXRCcmVhZGNydW1iIH19PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQnJlYWRjcnVtYkNvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuXG4vLyBDb21wb25lbnQgdG8gcmVuZGVyIHRoZSBicmVhZGNydW1iXG5leHBvcnQgY29uc3QgQnJlYWRjcnVtYkRpc3BsYXk6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCB7IGl0ZW1zIH0gPSB1c2VCcmVhZGNydW1iKCk7XG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBPbmx5IHJlbmRlciBvbiBjbGllbnQgc2lkZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldE1vdW50ZWQodHJ1ZSk7XG4gIH0sIFtdKTtcblxuICBpZiAoIW1vdW50ZWQgfHwgIWl0ZW1zIHx8IGl0ZW1zLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7IG1heFdpZHRoOiAnNzUwcHgnLCB3aWR0aDogJzc1MHB4JywgbWFyZ2luOiAnMCBhdXRvJywgdGV4dEFsaWduOiAnbGVmdCcgfX0+XG4gICAgICA8QnJlYWRjcnVtYiBpdGVtcz17aXRlbXN9IC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJyZWFkY3J1bWIiLCJCcmVhZGNydW1iQ29udGV4dCIsIml0ZW1zIiwic2V0QnJlYWRjcnVtYiIsInVzZUJyZWFkY3J1bWIiLCJCcmVhZGNydW1iUHJvdmlkZXIiLCJjaGlsZHJlbiIsInNldEl0ZW1zIiwidXNlQ2FsbGJhY2siLCJuZXdJdGVtcyIsIlByb3ZpZGVyIiwidmFsdWUiLCJCcmVhZGNydW1iRGlzcGxheSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwibGVuZ3RoIiwiZGl2Iiwic3R5bGUiLCJtYXhXaWR0aCIsIndpZHRoIiwibWFyZ2luIiwidGV4dEFsaWduIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/breadcrumb-provider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/breadcrumb.tsx":
/*!**************************************!*\
  !*** ./components/ui/breadcrumb.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Breadcrumb = ({ items })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n        className: \"breadcrumb\",\n        align: \"center\",\n        mb: 4,\n        fontSize: \"sm\",\n        color: \"gray.400\",\n        flexWrap: \"wrap\",\n        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                children: [\n                    index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        mx: 2,\n                        color: \"gray.500\",\n                        children: \"/\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, undefined),\n                    item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: item.href,\n                        className: \"breadcrumb-item\",\n                        style: {\n                            color: '#22d3ee',\n                            textDecoration: 'none'\n                        },\n                        onMouseOver: (e)=>e.currentTarget.style.color = '#67e8f9',\n                        onMouseOut: (e)=>e.currentTarget.style.color = '#22d3ee',\n                        children: item.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        color: \"gray.300\",\n                        fontWeight: \"medium\",\n                        children: item.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Breadcrumb);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/breadcrumb.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_hero_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/hero.css */ \"(pages-dir-node)/./styles/hero.css\");\n/* harmony import */ var _styles_hero_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_hero_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_animations_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/animations.css */ \"(pages-dir-node)/./styles/animations.css\");\n/* harmony import */ var _styles_animations_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_animations_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_breadcrumb_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../styles/breadcrumb.css */ \"(pages-dir-node)/./styles/breadcrumb.css\");\n/* harmony import */ var _styles_breadcrumb_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_breadcrumb_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_title_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/title.css */ \"(pages-dir-node)/./styles/title.css\");\n/* harmony import */ var _styles_title_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_title_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _styles_chapter_navigation_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../styles/chapter-navigation.css */ \"(pages-dir-node)/./styles/chapter-navigation.css\");\n/* harmony import */ var _styles_chapter_navigation_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_styles_chapter_navigation_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _styles_chapter_card_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../styles/chapter-card.css */ \"(pages-dir-node)/./styles/chapter-card.css\");\n/* harmony import */ var _styles_chapter_card_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_styles_chapter_card_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _styles_paydata_link_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../styles/paydata-link.css */ \"(pages-dir-node)/./styles/paydata-link.css\");\n/* harmony import */ var _styles_paydata_link_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_styles_paydata_link_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _styles_page_layout_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/page-layout.css */ \"(pages-dir-node)/./styles/page-layout.css\");\n/* harmony import */ var _styles_page_layout_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_page_layout_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _styles_content_restriction_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../styles/content-restriction.css */ \"(pages-dir-node)/./styles/content-restriction.css\");\n/* harmony import */ var _styles_content_restriction_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_styles_content_restriction_css__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _styles_outreach_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../styles/outreach.css */ \"(pages-dir-node)/./styles/outreach.css\");\n/* harmony import */ var _styles_outreach_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_styles_outreach_css__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _styles_navigation_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../styles/navigation.css */ \"(pages-dir-node)/./styles/navigation.css\");\n/* harmony import */ var _styles_navigation_css__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_styles_navigation_css__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _styles_content_responsive_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../styles/content-responsive.css */ \"(pages-dir-node)/./styles/content-responsive.css\");\n/* harmony import */ var _styles_content_responsive_css__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_styles_content_responsive_css__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _fontsource_rajdhani_400_css__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fontsource/rajdhani/400.css */ \"(pages-dir-node)/./node_modules/@fontsource/rajdhani/400.css\");\n/* harmony import */ var _fontsource_rajdhani_400_css__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_fontsource_rajdhani_400_css__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _fontsource_rajdhani_700_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @fontsource/rajdhani/700.css */ \"(pages-dir-node)/./node_modules/@fontsource/rajdhani/700.css\");\n/* harmony import */ var _fontsource_rajdhani_700_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_fontsource_rajdhani_700_css__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _fontsource_share_tech_mono_400_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @fontsource/share-tech-mono/400.css */ \"(pages-dir-node)/./node_modules/@fontsource/share-tech-mono/400.css\");\n/* harmony import */ var _fontsource_share_tech_mono_400_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_fontsource_share_tech_mono_400_css__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _fontsource_inter_400_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fontsource/inter/400.css */ \"(pages-dir-node)/./node_modules/@fontsource/inter/400.css\");\n/* harmony import */ var _fontsource_inter_400_css__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_fontsource_inter_400_css__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _fontsource_inter_600_css__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @fontsource/inter/600.css */ \"(pages-dir-node)/./node_modules/@fontsource/inter/600.css\");\n/* harmony import */ var _fontsource_inter_600_css__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(_fontsource_inter_600_css__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _chakra_ui_react_preset__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react/preset */ \"@chakra-ui/react/preset\");\n/* harmony import */ var _components_ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ../components/ui/breadcrumb-provider */ \"(pages-dir-node)/./components/ui/breadcrumb-provider.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var _components_ui_RestrictionProvider__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../components/ui/RestrictionProvider */ \"(pages-dir-node)/./components/ui/RestrictionProvider.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ../components/layout/Header */ \"(pages-dir-node)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ../components/layout/Footer */ \"(pages-dir-node)/./components/layout/Footer.tsx\");\n/* harmony import */ var _vercel_analytics_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @vercel/analytics/react */ \"@vercel/analytics/react\");\n/* harmony import */ var _vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @vercel/speed-insights/next */ \"@vercel/speed-insights/next\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_29___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_29__);\n/* harmony import */ var _components_performance_IntelligentPagePrefetcher__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ../components/performance/IntelligentPagePrefetcher */ \"(pages-dir-node)/./components/performance/IntelligentPagePrefetcher.tsx\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_31___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_31__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__, _chakra_ui_react_preset__WEBPACK_IMPORTED_MODULE_20__, _components_ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_21__, _components_ui_RestrictionProvider__WEBPACK_IMPORTED_MODULE_24__, _components_layout_Header__WEBPACK_IMPORTED_MODULE_25__, _components_layout_Footer__WEBPACK_IMPORTED_MODULE_26__, _vercel_analytics_react__WEBPACK_IMPORTED_MODULE_27__, _vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_28__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__, _chakra_ui_react_preset__WEBPACK_IMPORTED_MODULE_20__, _components_ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_21__, _components_ui_RestrictionProvider__WEBPACK_IMPORTED_MODULE_24__, _components_layout_Header__WEBPACK_IMPORTED_MODULE_25__, _components_layout_Footer__WEBPACK_IMPORTED_MODULE_26__, _vercel_analytics_react__WEBPACK_IMPORTED_MODULE_27__, _vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_28__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Added responsive content CSS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import page prefetching component\n\n\n// Client-side only DebugControls component\nconst DebugControlsComponent = next_dynamic__WEBPACK_IMPORTED_MODULE_23___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-node_components_ui_DebugControls_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/ui/DebugControls */ \"(pages-dir-node)/./components/ui/DebugControls.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\_app.tsx -> \" + \"../components/ui/DebugControls\"\n        ]\n    },\n    ssr: false\n});\nfunction MyApp({ Component, pageProps }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_29__.useRouter)();\n    const [contentVisible, setContentVisible] = (0,react__WEBPACK_IMPORTED_MODULE_22__.useState)(true);\n    // Simplified route change handling\n    (0,react__WEBPACK_IMPORTED_MODULE_22__.useEffect)({\n        \"MyApp.useEffect\": ()=>{\n            const handleRouteChangeStart = {\n                \"MyApp.useEffect.handleRouteChangeStart\": (url)=>{\n                    // Prevent handling when navigating to the same URL\n                    if (url === window.location.pathname) return;\n                    // Simple fade effect\n                    setContentVisible(false);\n                    // Close the mobile menu on navigation\n                    document.dispatchEvent(new Event('closeNavigationMenu'));\n                }\n            }[\"MyApp.useEffect.handleRouteChangeStart\"];\n            const handleRouteChangeComplete = {\n                \"MyApp.useEffect.handleRouteChangeComplete\": ()=>{\n                    setContentVisible(true);\n                }\n            }[\"MyApp.useEffect.handleRouteChangeComplete\"];\n            const handleRouteChangeError = {\n                \"MyApp.useEffect.handleRouteChangeError\": ()=>{\n                    setContentVisible(true);\n                }\n            }[\"MyApp.useEffect.handleRouteChangeError\"];\n            // Add Next.js router event listeners\n            router.events.on('routeChangeStart', handleRouteChangeStart);\n            router.events.on('routeChangeComplete', handleRouteChangeComplete);\n            router.events.on('routeChangeError', handleRouteChangeError);\n            // Clean up event listeners\n            return ({\n                \"MyApp.useEffect\": ()=>{\n                    router.events.off('routeChangeStart', handleRouteChangeStart);\n                    router.events.off('routeChangeComplete', handleRouteChangeComplete);\n                    router.events.off('routeChangeError', handleRouteChangeError);\n                }\n            })[\"MyApp.useEffect\"];\n        }\n    }[\"MyApp.useEffect\"], [\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.ChakraProvider, {\n        value: _chakra_ui_react_preset__WEBPACK_IMPORTED_MODULE_20__.system,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_31___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Horizon City | A Cyberpunk Anthology Universe\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://www.horizon-city.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://www.horizon-city.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-UA-Compatible\",\n                        content: \"IE=edge\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb_provider__WEBPACK_IMPORTED_MODULE_21__.BreadcrumbProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_RestrictionProvider__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen cyberpunk-bg\",\n                        style: {\n                            opacity: contentVisible ? 1 : 0,\n                            transition: 'opacity 0.3s ease-in-out',\n                            overflow: 'hidden',\n                            width: '100%'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                    ...pageProps\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugControlsComponent, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vercel_analytics_react__WEBPACK_IMPORTED_MODULE_27__.Analytics, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_28__.SpeedInsights, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_IntelligentPagePrefetcher__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_app.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/animations.css":
/*!*******************************!*\
  !*** ./styles/animations.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/breadcrumb.css":
/*!*******************************!*\
  !*** ./styles/breadcrumb.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/chapter-card.css":
/*!*********************************!*\
  !*** ./styles/chapter-card.css ***!
  \*********************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/chapter-navigation.css":
/*!***************************************!*\
  !*** ./styles/chapter-navigation.css ***!
  \***************************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/content-responsive.css":
/*!***************************************!*\
  !*** ./styles/content-responsive.css ***!
  \***************************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/content-restriction.css":
/*!****************************************!*\
  !*** ./styles/content-restriction.css ***!
  \****************************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/hero.css":
/*!*************************!*\
  !*** ./styles/hero.css ***!
  \*************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/navigation.css":
/*!*******************************!*\
  !*** ./styles/navigation.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/outreach.css":
/*!*****************************!*\
  !*** ./styles/outreach.css ***!
  \*****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/page-layout.css":
/*!********************************!*\
  !*** ./styles/page-layout.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/paydata-link.css":
/*!*********************************!*\
  !*** ./styles/paydata-link.css ***!
  \*********************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./styles/title.css":
/*!**************************!*\
  !*** ./styles/title.css ***!
  \**************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./utils/access-badge-manager.ts":
/*!***************************************!*\
  !*** ./utils/access-badge-manager.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllUnlockedStorySlugs: () => (/* binding */ getAllUnlockedStorySlugs),\n/* harmony export */   getUnlockedCharacterSlugs: () => (/* binding */ getUnlockedCharacterSlugs),\n/* harmony export */   getUnlockedLocationSlugs: () => (/* binding */ getUnlockedLocationSlugs),\n/* harmony export */   getUnlockedStorySlugs: () => (/* binding */ getUnlockedStorySlugs),\n/* harmony export */   getUnlockedTechnologySlugs: () => (/* binding */ getUnlockedTechnologySlugs),\n/* harmony export */   getUnlockedThemeSlugs: () => (/* binding */ getUnlockedThemeSlugs),\n/* harmony export */   hasCharacterAccess: () => (/* binding */ hasCharacterAccess),\n/* harmony export */   hasLocationAccess: () => (/* binding */ hasLocationAccess),\n/* harmony export */   hasSeriesAccess: () => (/* binding */ hasSeriesAccess),\n/* harmony export */   hasStoryAccess: () => (/* binding */ hasStoryAccess),\n/* harmony export */   hasTechnologyAccess: () => (/* binding */ hasTechnologyAccess),\n/* harmony export */   hasThemeAccess: () => (/* binding */ hasThemeAccess),\n/* harmony export */   processStoryVisit: () => (/* binding */ processStoryVisit)\n/* harmony export */ });\n/* harmony import */ var _access_badges__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./access-badges */ \"(pages-dir-node)/./utils/access-badges.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_access_badges__WEBPACK_IMPORTED_MODULE_0__]);\n_access_badges__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n/**\n * Process a story visit and unlock any badges specified in the metadata\n */ function processStoryVisit(storyData) {\n    // Ensure we have a valid series\n    if (!storyData.series) {\n        console.error('No series provided for story:', storyData.title || storyData.slug);\n        return {\n            newBadges: [],\n            totalBadges: [],\n            newCharacterBadges: [],\n            newLocationBadges: [],\n            newStoryBadges: [],\n            newSeriesBadges: [],\n            newTechnologyBadges: [],\n            newThemeBadges: [],\n            newSeriesBadge: null\n        };\n    }\n    // Check if we're on a story page to determine if we should delay badge saving\n    const isStoryPage =  false && 0;\n    console.log('Processing story visit for:', storyData.title || storyData.slug, 'in series:', storyData.series);\n    console.log('Story badges:', storyData.storyBadges || storyData.accessBadges?.stories || []);\n    console.log('Character badges:', storyData.characterBadges || storyData.accessBadges?.characters || []);\n    console.log('Location badges:', storyData.locationBadges || storyData.accessBadges?.locations || []);\n    // Use the new addBadgesForStory function with a 5-second delay if on a story page\n    const result = (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.addBadgesForStory)({\n        series: storyData.series,\n        title: storyData.title,\n        id: storyData.slug,\n        slug: storyData.slug,\n        characterBadges: storyData.characterBadges || storyData.accessBadges?.characters || [],\n        locationBadges: storyData.locationBadges || storyData.accessBadges?.locations || [],\n        storyBadges: storyData.storyBadges || storyData.accessBadges?.stories || [],\n        seriesBadges: storyData.seriesBadges || [],\n        technologyBadges: storyData.technologyBadges || storyData.accessBadges?.technologies || [],\n        themeBadges: storyData.themeBadges || storyData.accessBadges?.themes || []\n    }, isStoryPage ? 5000 : 0); // 5-second delay if on a story page\n    // Set up a timeout to process badges again after 10 seconds\n    // This replaces the setInterval that was removed\n    if (isStoryPage && \"undefined\" !== 'undefined') {}\n    // Combine all new badges into a single array\n    const newBadges = [];\n    if (result.newSeriesBadge) {\n        newBadges.push(result.newSeriesBadge);\n    }\n    newBadges.push(...result.newCharacterBadges);\n    newBadges.push(...result.newLocationBadges);\n    newBadges.push(...result.newStoryBadges);\n    newBadges.push(...result.newSeriesBadges);\n    newBadges.push(...result.newTechnologyBadges);\n    newBadges.push(...result.newThemeBadges);\n    // Combine all badges from all types\n    const allBadges = [\n        ...(0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getCharacterBadges)(),\n        ...(0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getLocationBadges)(),\n        ...(0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getAllStoryBadges)(),\n        ...(0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getSeriesBadges)()\n    ];\n    return {\n        newBadges,\n        totalBadges: allBadges,\n        newCharacterBadges: result.newCharacterBadges,\n        newLocationBadges: result.newLocationBadges,\n        newStoryBadges: result.newStoryBadges,\n        newSeriesBadges: result.newSeriesBadges,\n        newTechnologyBadges: result.newTechnologyBadges,\n        newThemeBadges: result.newThemeBadges,\n        newSeriesBadge: result.newSeriesBadge\n    };\n}\n/**\n * Check if a user has access to a specific character\n */ function hasCharacterAccess(characterSlug) {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.hasCharacterAccess)(characterSlug);\n}\n/**\n * Check if a user has access to a specific location\n */ function hasLocationAccess(locationSlug) {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.hasLocationAccess)(locationSlug);\n}\n/**\n * Check if a user has access to a specific story\n *\n * This function checks if a user has access to a story based on:\n * 1. If it's the Clone story (always accessible)\n * 2. If the user has the badge from the previous story in the series\n * 3. If the user has the specific badge for this story\n */ function hasStoryAccess(seriesSlug, storySlug) {\n    // Delegate to the core function in access-badges.ts\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.hasStoryAccess)(seriesSlug, storySlug);\n}\n/**\n * Check if a user has access to a specific series\n */ function hasSeriesAccess(seriesSlug) {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.hasSeriesAccess)(seriesSlug);\n}\n/**\n * Check if a user has access to a specific technology\n */ function hasTechnologyAccess(technologySlug) {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.hasTechnologyAccess)(technologySlug);\n}\n/**\n * Check if a user has access to a specific theme\n */ function hasThemeAccess(themeSlug) {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.hasThemeAccess)(themeSlug);\n}\n/**\n * Get a list of all unlocked character slugs\n */ function getUnlockedCharacterSlugs() {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getCharacterBadges)().map((badge)=>badge.id);\n}\n/**\n * Get a list of all unlocked location slugs\n */ function getUnlockedLocationSlugs() {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getLocationBadges)().map((badge)=>badge.id);\n}\n/**\n * Get a list of all unlocked technology slugs\n */ function getUnlockedTechnologySlugs() {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getTechnologyBadges)().map((badge)=>badge.id);\n}\n/**\n * Get a list of all unlocked theme slugs\n */ function getUnlockedThemeSlugs() {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getThemeBadges)().map((badge)=>badge.id);\n}\n/**\n * Get a list of all unlocked story slugs for a specific series\n */ function getUnlockedStorySlugs(seriesSlug) {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getStoryBadges)(seriesSlug).map((badge)=>badge.id);\n}\n/**\n * Get a list of all unlocked story slugs across all series\n */ function getAllUnlockedStorySlugs() {\n    return (0,_access_badges__WEBPACK_IMPORTED_MODULE_0__.getAllStoryBadges)().map((badge)=>badge.id);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./utils/access-badge-manager.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./utils/access-badges.ts":
/*!********************************!*\
  !*** ./utils/access-badges.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BOOK_SLUGS: () => (/* binding */ BOOK_SLUGS),\n/* harmony export */   BadgeType: () => (/* binding */ BadgeType),\n/* harmony export */   addBadgesForStory: () => (/* binding */ addBadgesForStory),\n/* harmony export */   addCharacterBadge: () => (/* binding */ addCharacterBadge),\n/* harmony export */   addLocationBadge: () => (/* binding */ addLocationBadge),\n/* harmony export */   addSeriesBadge: () => (/* binding */ addSeriesBadge),\n/* harmony export */   addStoryBadge: () => (/* binding */ addStoryBadge),\n/* harmony export */   addTechnologyBadge: () => (/* binding */ addTechnologyBadge),\n/* harmony export */   addThemeBadge: () => (/* binding */ addThemeBadge),\n/* harmony export */   debugMissingCharacterBadges: () => (/* binding */ debugMissingCharacterBadges),\n/* harmony export */   formatBadgeName: () => (/* binding */ formatBadgeName),\n/* harmony export */   fuzzyMatchBadgeId: () => (/* binding */ fuzzyMatchBadgeId),\n/* harmony export */   getAllStoryBadges: () => (/* binding */ getAllStoryBadges),\n/* harmony export */   getCharacterBadges: () => (/* binding */ getCharacterBadges),\n/* harmony export */   getLocationBadges: () => (/* binding */ getLocationBadges),\n/* harmony export */   getSeriesBadges: () => (/* binding */ getSeriesBadges),\n/* harmony export */   getStoryBadges: () => (/* binding */ getStoryBadges),\n/* harmony export */   getTechnologyBadges: () => (/* binding */ getTechnologyBadges),\n/* harmony export */   getThemeBadges: () => (/* binding */ getThemeBadges),\n/* harmony export */   hasAnyBadge: () => (/* binding */ hasAnyBadge),\n/* harmony export */   hasAnyCharacterOrLocationBadge: () => (/* binding */ hasAnyCharacterOrLocationBadge),\n/* harmony export */   hasCharacterAccess: () => (/* binding */ hasCharacterAccess),\n/* harmony export */   hasCharacterBadge: () => (/* binding */ hasCharacterBadge),\n/* harmony export */   hasLocationAccess: () => (/* binding */ hasLocationAccess),\n/* harmony export */   hasLocationBadge: () => (/* binding */ hasLocationBadge),\n/* harmony export */   hasSeriesAccess: () => (/* binding */ hasSeriesAccess),\n/* harmony export */   hasSeriesBadge: () => (/* binding */ hasSeriesBadge),\n/* harmony export */   hasStoryAccess: () => (/* binding */ hasStoryAccess),\n/* harmony export */   hasStoryBadge: () => (/* binding */ hasStoryBadge),\n/* harmony export */   hasTechnologyAccess: () => (/* binding */ hasTechnologyAccess),\n/* harmony export */   hasTechnologyBadge: () => (/* binding */ hasTechnologyBadge),\n/* harmony export */   hasThemeAccess: () => (/* binding */ hasThemeAccess),\n/* harmony export */   hasThemeBadge: () => (/* binding */ hasThemeBadge),\n/* harmony export */   processDelayedBadges: () => (/* binding */ processDelayedBadges),\n/* harmony export */   queueBadgeForDelayedSaving: () => (/* binding */ queueBadgeForDelayedSaving),\n/* harmony export */   resetAllBadges: () => (/* binding */ resetAllBadges)\n/* harmony export */ });\n/* harmony import */ var _cookie_manager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cookie-manager */ \"(pages-dir-node)/./utils/cookie-manager.ts\");\n/* harmony import */ var _debug_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./debug-manager */ \"(pages-dir-node)/./utils/debug-manager.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_cookie_manager__WEBPACK_IMPORTED_MODULE_0__, _debug_manager__WEBPACK_IMPORTED_MODULE_1__]);\n([_cookie_manager__WEBPACK_IMPORTED_MODULE_0__, _debug_manager__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Global queue for delayed badge saving\nlet delayedBadgeQueue = [];\n// Types of badges\nvar BadgeType = /*#__PURE__*/ function(BadgeType) {\n    BadgeType[\"CHARACTER\"] = \"character\";\n    BadgeType[\"LOCATION\"] = \"location\";\n    BadgeType[\"SERIES\"] = \"series\";\n    BadgeType[\"STORY\"] = \"story\";\n    BadgeType[\"TECHNOLOGY\"] = \"technology\";\n    BadgeType[\"THEME\"] = \"theme\";\n    return BadgeType;\n}({});\n// Cookie prefix for badge storage\nconst COOKIE_PREFIX = 'horizon_badges_';\n// List of valid book slugs\nconst BOOK_SLUGS = [\n    'horizons-hope',\n    'horizons-edge',\n    'horizons-dawn',\n    'horizons-end'\n];\n// Create cookie managers for each badge type\nconst cookieManagers = {\n    [\"character\"]: new _cookie_manager__WEBPACK_IMPORTED_MODULE_0__.CookieManager(`${COOKIE_PREFIX}${\"character\"}`),\n    [\"location\"]: new _cookie_manager__WEBPACK_IMPORTED_MODULE_0__.CookieManager(`${COOKIE_PREFIX}${\"location\"}`),\n    [\"series\"]: new _cookie_manager__WEBPACK_IMPORTED_MODULE_0__.CookieManager(`${COOKIE_PREFIX}${\"series\"}`),\n    [\"technology\"]: new _cookie_manager__WEBPACK_IMPORTED_MODULE_0__.CookieManager(`${COOKIE_PREFIX}${\"technology\"}`),\n    [\"theme\"]: new _cookie_manager__WEBPACK_IMPORTED_MODULE_0__.CookieManager(`${COOKIE_PREFIX}${\"theme\"}`)\n};\n// Create book-specific cookie managers for story badges\nconst storyBadgeCookieManagers = {};\nBOOK_SLUGS.forEach((bookSlug)=>{\n    storyBadgeCookieManagers[bookSlug] = new _cookie_manager__WEBPACK_IMPORTED_MODULE_0__.CookieManager(`${COOKIE_PREFIX}${\"story\"}_${bookSlug}`);\n});\n/**\n * Save badges to cookies by type\n * Uses CookieManager to automatically handle splitting across multiple cookies if needed\n */ function saveBadgesToCookie(badges, type) {\n    try {\n        // Use the cookie manager to save badges\n        cookieManagers[type].set(badges);\n    } catch (e) {\n        console.error(`Error saving ${type} badges to cookies:`, e);\n    }\n}\n/**\n * Get badges from cookies by type\n * Uses CookieManager to automatically handle joining from multiple cookies if needed\n */ function getBadgesFromCookie(type) {\n    try {\n        // Use the cookie manager to get badges\n        const badges = cookieManagers[type].get();\n        return badges || [];\n    } catch (e) {\n        console.error(`Error getting ${type} badges from cookies:`, e);\n        return [];\n    }\n}\n/**\n * Get character badges\n */ function getCharacterBadges() {\n    return getBadgesFromCookie(\"character\");\n}\n/**\n * Get location badges\n */ function getLocationBadges() {\n    return getBadgesFromCookie(\"location\");\n}\n/**\n * Get series badges\n */ function getSeriesBadges() {\n    return getBadgesFromCookie(\"series\");\n}\n/**\n * Helper function to determine which book a series belongs to\n */ function getBookSlugFromSeries(seriesSlug) {\n    // Handle undefined or empty series slug\n    if (!seriesSlug) {\n        return null;\n    }\n    for (const bookSlug of BOOK_SLUGS){\n        if (seriesSlug.startsWith(bookSlug)) {\n            return bookSlug;\n        }\n    }\n    return null;\n}\n/**\n * Save story badges to cookies by book\n */ function saveStoryBadgesToCookie(badges, bookSlug) {\n    try {\n        // Use the book-specific cookie manager to save badges\n        storyBadgeCookieManagers[bookSlug].set(badges);\n    } catch (e) {\n        console.error(`Error saving story badges for book ${bookSlug} to cookies:`, e);\n    }\n}\n/**\n * Get story badges from cookies by book\n */ function getStoryBadgesFromCookie(bookSlug) {\n    try {\n        // Check if the cookie manager exists for this book\n        if (!storyBadgeCookieManagers[bookSlug]) {\n            return [];\n        }\n        // Use the book-specific cookie manager to get badges\n        const badges = storyBadgeCookieManagers[bookSlug].get();\n        return badges || [];\n    } catch (e) {\n        console.error(`Error getting story badges for book ${bookSlug} from cookies:`, e);\n        return [];\n    }\n}\n/**\n * Get story badges for a specific series\n * @param seriesSlug The series slug (e.g., 'horizons-hope')\n */ function getStoryBadges(seriesSlug) {\n    // If no series slug is provided, return all story badges\n    if (!seriesSlug) {\n        return getAllStoryBadges();\n    }\n    // Determine which book this series belongs to\n    const bookSlug = getBookSlugFromSeries(seriesSlug);\n    if (!bookSlug) {\n        return [];\n    }\n    return getStoryBadgesFromCookie(bookSlug);\n}\n/**\n * Get all story badges across all books\n */ function getAllStoryBadges() {\n    let allBadges = [];\n    BOOK_SLUGS.forEach((bookSlug)=>{\n        const badges = getStoryBadgesFromCookie(bookSlug);\n        allBadges = [\n            ...allBadges,\n            ...badges\n        ];\n    });\n    return allBadges;\n}\n/**\n * Get technology badges\n */ function getTechnologyBadges() {\n    return getBadgesFromCookie(\"technology\");\n}\n/**\n * Get theme badges\n */ function getThemeBadges() {\n    return getBadgesFromCookie(\"theme\");\n}\n/**\n * Add a character badge\n */ function addCharacterBadge(id) {\n    const badges = getCharacterBadges();\n    // Check if badge already exists using fuzzy matching\n    if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n        return badges;\n    }\n    // Format badge name with proper capitalization\n    const name = formatBadgeName(id);\n    // Create new badge\n    const newBadge = {\n        id,\n        name\n    };\n    // Add the new badge\n    const updatedBadges = [\n        ...badges,\n        newBadge\n    ];\n    // Save to storage\n    saveBadgesToCookie(updatedBadges, \"character\");\n    return updatedBadges;\n}\n/**\n * Add a location badge\n */ function addLocationBadge(id) {\n    const badges = getLocationBadges();\n    // Check if badge already exists using fuzzy matching\n    if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n        return badges;\n    }\n    // Format badge name with proper capitalization\n    const name = formatBadgeName(id);\n    // Create new badge\n    const newBadge = {\n        id,\n        name\n    };\n    // Add the new badge\n    const updatedBadges = [\n        ...badges,\n        newBadge\n    ];\n    // Save to storage\n    saveBadgesToCookie(updatedBadges, \"location\");\n    return updatedBadges;\n}\n/**\n * Add a series badge\n */ function addSeriesBadge(id) {\n    const badges = getSeriesBadges();\n    // Check if badge already exists using fuzzy matching\n    if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n        return badges;\n    }\n    // Format badge name with proper capitalization\n    const name = formatBadgeName(id);\n    // Create new badge\n    const newBadge = {\n        id,\n        name\n    };\n    // Add the new badge\n    const updatedBadges = [\n        ...badges,\n        newBadge\n    ];\n    // Save to storage\n    saveBadgesToCookie(updatedBadges, \"series\");\n    return updatedBadges;\n}\n/**\n * Add a story badge for a specific series\n * @param seriesSlug The series slug (e.g., 'horizons-hope')\n * @param id The badge ID\n */ function addStoryBadge(seriesSlug, id) {\n    // Handle undefined series slug\n    if (!seriesSlug) {\n        return [];\n    }\n    // Determine which book this series belongs to\n    const bookSlug = getBookSlugFromSeries(seriesSlug);\n    if (!bookSlug) {\n        // Fallback to using horizons-hope for backward compatibility\n        const fallbackBookSlug = 'horizons-hope';\n        const badges = getStoryBadgesFromCookie(fallbackBookSlug);\n        // Check if badge already exists using fuzzy matching\n        if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n            return badges;\n        }\n        // Format badge name with proper capitalization\n        const name = formatBadgeName(id);\n        // Create new badge\n        const newBadge = {\n            id,\n            name\n        };\n        // Add the new badge\n        const updatedBadges = [\n            ...badges,\n            newBadge\n        ];\n        // Save to storage\n        saveStoryBadgesToCookie(updatedBadges, fallbackBookSlug);\n        return updatedBadges;\n    }\n    const badges = getStoryBadgesFromCookie(bookSlug);\n    // Check if badge already exists using fuzzy matching\n    if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n        return badges;\n    }\n    // Format badge name with proper capitalization\n    const name = formatBadgeName(id);\n    // Create new badge\n    const newBadge = {\n        id,\n        name\n    };\n    // Add the new badge\n    const updatedBadges = [\n        ...badges,\n        newBadge\n    ];\n    // Save to storage\n    saveStoryBadgesToCookie(updatedBadges, bookSlug);\n    return updatedBadges;\n}\n/**\n * Add a technology badge\n */ function addTechnologyBadge(id) {\n    const badges = getTechnologyBadges();\n    // Check if badge already exists using fuzzy matching\n    if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n        return badges;\n    }\n    // Format badge name with proper capitalization\n    const name = formatBadgeName(id);\n    // Create new badge\n    const newBadge = {\n        id,\n        name\n    };\n    // Add the new badge\n    const updatedBadges = [\n        ...badges,\n        newBadge\n    ];\n    // Save to storage\n    saveBadgesToCookie(updatedBadges, \"technology\");\n    return updatedBadges;\n}\n/**\n * Add a theme badge\n */ function addThemeBadge(id) {\n    const badges = getThemeBadges();\n    // Check if badge already exists using fuzzy matching\n    if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n        return badges;\n    }\n    // Format badge name with proper capitalization\n    const name = formatBadgeName(id);\n    // Create new badge\n    const newBadge = {\n        id,\n        name\n    };\n    // Add the new badge\n    const updatedBadges = [\n        ...badges,\n        newBadge\n    ];\n    // Save to storage\n    saveBadgesToCookie(updatedBadges, \"theme\");\n    return updatedBadges;\n}\n/**\n * Fuzzy match a badge ID against a target ID\n * This handles cases like:\n * - \"akiko-takahashi\" matching \"akiko\"\n * - \"clone\" matching \"01-clone\"\n * - Exact matches\n */ function fuzzyMatchBadgeId(badgeId, targetId) {\n    // Normalize both strings to lowercase for case-insensitive matching\n    const normalizedBadgeId = badgeId.toLowerCase();\n    const normalizedTargetId = targetId.toLowerCase();\n    // Check for exact match first\n    if (normalizedBadgeId === normalizedTargetId) {\n        return true;\n    }\n    // Handle numbered prefixes (e.g., \"01-clone\" matching \"clone\")\n    // Extract the part after any potential number prefix\n    const badgeIdWithoutPrefix = normalizedBadgeId.replace(/^\\d+-/, '');\n    const targetIdWithoutPrefix = normalizedTargetId.replace(/^\\d+-/, '');\n    // Check if the parts without prefixes match\n    const prefixesMatch = badgeIdWithoutPrefix === targetIdWithoutPrefix;\n    if (prefixesMatch) {\n        return true;\n    }\n    // No match found\n    return false;\n}\n/**\n * Check if user has a character badge\n */ function hasCharacterBadge(id) {\n    const badges = getCharacterBadges();\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id));\n}\n/**\n * Check if user has a location badge\n */ function hasLocationBadge(id) {\n    const badges = getLocationBadges();\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id));\n}\n/**\n * Check if user has a series badge\n */ function hasSeriesBadge(id) {\n    const badges = getSeriesBadges();\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id));\n}\n/**\n * Check if user has a story badge\n * @param seriesSlug The series slug (e.g., 'horizons-hope')\n * @param id The badge ID\n */ function hasStoryBadge(seriesSlug, id) {\n    // Special case for Clone story: it's always accessible\n    if (id.toLowerCase().includes('clone') || id === '01-clone') {\n        return true;\n    }\n    // If no series slug is provided, check all books\n    if (!seriesSlug) {\n        // Check each book for the badge\n        for (const bookSlug of BOOK_SLUGS){\n            const badges = getStoryBadgesFromCookie(bookSlug);\n            if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n                return true;\n            }\n        }\n        return false;\n    }\n    // Determine which book this series belongs to\n    const bookSlug = getBookSlugFromSeries(seriesSlug);\n    if (!bookSlug) {\n        // Fallback to checking all books\n        for (const bookSlug of BOOK_SLUGS){\n            const badges = getStoryBadgesFromCookie(bookSlug);\n            if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id))) {\n                return true;\n            }\n        }\n        return false;\n    }\n    const badges = getStoryBadgesFromCookie(bookSlug);\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id));\n}\n/**\n * Check if user has a technology badge\n */ function hasTechnologyBadge(id) {\n    const badges = getTechnologyBadges();\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id));\n}\n/**\n * Check if user has a theme badge\n */ function hasThemeBadge(id) {\n    const badges = getThemeBadges();\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, id));\n}\n/**\n * Add a badge to the delayed queue instead of saving immediately\n */ function queueBadgeForDelayedSaving(type, id, series) {\n    // For story badges, we need to determine the book slug\n    let bookSlug;\n    if (type === 'story' && series) {\n        bookSlug = getBookSlugFromSeries(series);\n    }\n    // Add to queue - ensuring bookSlug is undefined if it's null\n    // This avoids type issues when using the queue later\n    delayedBadgeQueue.push({\n        type,\n        id,\n        series,\n        bookSlug: bookSlug || undefined\n    });\n}\n/**\n * Process all queued badges and save them to cookies\n * This should be called when navigating away from a story page\n * Will only process badges if the loading screen is not visible\n */ function processDelayedBadges() {\n    if (delayedBadgeQueue.length === 0) {\n        return;\n    }\n    // Function to check if loading screen is visible\n    const isLoadingScreenVisible = ()=>{\n        if (typeof document === 'undefined') return false;\n        const loadingScreen = document.querySelector('[data-loading-screen=\"true\"]');\n        return !!loadingScreen;\n    };\n    // If loading screen is visible, delay processing\n    if (isLoadingScreenVisible()) {\n        console.log('Loading screen still visible, delaying badge processing');\n        // Try again in a short while\n        setTimeout(processDelayedBadges, 500);\n        return;\n    }\n    console.log('Processing delayed badges now that loading screen is gone');\n    // Process each queued badge\n    delayedBadgeQueue.forEach((badge)=>{\n        if (badge.type === 'story') {\n            // Handle story badges\n            if (badge.series) {\n                addStoryBadge(badge.series, badge.id);\n            }\n        } else {\n            // Handle other badge types\n            switch(badge.type){\n                case \"character\":\n                    addCharacterBadge(badge.id);\n                    break;\n                case \"location\":\n                    addLocationBadge(badge.id);\n                    break;\n                case \"series\":\n                    addSeriesBadge(badge.id);\n                    break;\n                case \"technology\":\n                    addTechnologyBadge(badge.id);\n                    break;\n                case \"theme\":\n                    addThemeBadge(badge.id);\n                    break;\n            }\n        }\n    });\n    // Clear the queue\n    delayedBadgeQueue = [];\n}\n/**\n * Check if user has access to a character slug (with fuzzy matching)\n */ function hasCharacterAccess(slug) {\n    const badges = getCharacterBadges();\n    // Use fuzzy matching for all badges\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, slug));\n}\n/**\n * Debug function to identify missing character badges\n * This function compares the list of all character slugs with the unlocked character badges\n * and logs the missing character badges\n *\n * @param allCharacterSlugs Array of all character slugs from the server\n */ function debugMissingCharacterBadges(allCharacterSlugs) {\n    // Get all unlocked character badges\n    const unlockedBadges = getCharacterBadges();\n    const unlockedSlugs = unlockedBadges.map((badge)=>badge.id);\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`===== DEBUG MISSING CHARACTER BADGES =====`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Total character slugs: ${allCharacterSlugs.length}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Unlocked character badges: ${unlockedBadges.length}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    // Log all character slugs for inspection\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`All character slugs: ${allCharacterSlugs.join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Unlocked badge IDs: ${unlockedSlugs.join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    // Check for exact matches first (without fuzzy matching)\n    const exactMissingSlugs = allCharacterSlugs.filter((slug)=>!unlockedSlugs.includes(slug));\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Missing character badges (exact match): ${exactMissingSlugs.length}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    if (exactMissingSlugs.length > 0) {\n        (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Missing character slugs (exact match): ${exactMissingSlugs.join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n        // For each missing slug, check which badge it's fuzzy matching with\n        exactMissingSlugs.forEach((missingSlug)=>{\n            (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Checking fuzzy matches for missing slug: ${missingSlug}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n            const fuzzyMatches = unlockedBadges.filter((badge)=>fuzzyMatchBadgeId(badge.id, missingSlug));\n            if (fuzzyMatches.length > 0) {\n                (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Fuzzy matches found for ${missingSlug}: ${fuzzyMatches.map((b)=>b.id).join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                // Check each fuzzy match in detail\n                fuzzyMatches.forEach((match)=>{\n                    // Normalize both strings to lowercase for case-insensitive matching\n                    const normalizedBadgeId = match.id.toLowerCase();\n                    const normalizedTargetId = missingSlug.toLowerCase();\n                    // Check for exact match\n                    const exactMatch = normalizedBadgeId === normalizedTargetId;\n                    // Check if one contains the other\n                    const badgeContainsTarget = normalizedBadgeId.includes(normalizedTargetId);\n                    const targetContainsBadge = normalizedTargetId.includes(normalizedBadgeId);\n                    // Handle numbered prefixes\n                    const badgeIdWithoutPrefix = normalizedBadgeId.replace(/^\\d+-/, '');\n                    const targetIdWithoutPrefix = normalizedTargetId.replace(/^\\d+-/, '');\n                    const prefixesMatch = badgeIdWithoutPrefix === targetIdWithoutPrefix;\n                    // Check if one contains the other after removing prefixes\n                    const badgePrefixContainsTargetPrefix = badgeIdWithoutPrefix.includes(targetIdWithoutPrefix);\n                    const targetPrefixContainsBadgePrefix = targetIdWithoutPrefix.includes(badgeIdWithoutPrefix);\n                    // Handle type prefixes\n                    const badgeIdWithoutTypePrefix = normalizedBadgeId.replace(/^[a-z]+-/, '');\n                    const targetIdWithoutTypePrefix = normalizedTargetId.replace(/^[a-z]+-/, '');\n                    const typePrefixesMatch = badgeIdWithoutTypePrefix === targetIdWithoutTypePrefix;\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Match details for ${missingSlug} and ${match.id}:`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`  Exact match: ${exactMatch}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`  Badge contains target: ${badgeContainsTarget}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`  Target contains badge: ${targetContainsBadge}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`  Prefixes match: ${prefixesMatch}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`  Badge prefix contains target prefix: ${badgePrefixContainsTargetPrefix}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`  Target prefix contains badge prefix: ${targetPrefixContainsBadgePrefix}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`  Type prefixes match: ${typePrefixesMatch}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n                });\n            } else {\n                (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`No fuzzy matches found for ${missingSlug}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n            }\n        });\n    }\n    // Find missing character slugs using fuzzy matching\n    const missingSlugs = allCharacterSlugs.filter((slug)=>{\n        // Check if this slug is unlocked using the same fuzzy matching logic\n        const isUnlocked = unlockedBadges.some((badge)=>fuzzyMatchBadgeId(badge.id, slug));\n        return !isUnlocked;\n    });\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Missing character badges (fuzzy match): ${missingSlugs.length}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    if (missingSlugs.length > 0) {\n        (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Missing character slugs (fuzzy match): ${missingSlugs.join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    }\n    // Check for duplicate slugs in the all character slugs list\n    const slugCounts = {};\n    allCharacterSlugs.forEach((slug)=>{\n        slugCounts[slug] = (slugCounts[slug] || 0) + 1;\n    });\n    const duplicateSlugs = Object.entries(slugCounts).filter(([_, count])=>count > 1).map(([slug, _])=>slug);\n    if (duplicateSlugs.length > 0) {\n        (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Duplicate character slugs found: ${duplicateSlugs.join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    }\n    // Check for duplicate IDs in the unlocked badges\n    const badgeIdCounts = {};\n    unlockedSlugs.forEach((id)=>{\n        badgeIdCounts[id] = (badgeIdCounts[id] || 0) + 1;\n    });\n    const duplicateBadgeIds = Object.entries(badgeIdCounts).filter(([_, count])=>count > 1).map(([id, _])=>id);\n    if (duplicateBadgeIds.length > 0) {\n        (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Duplicate badge IDs found: ${duplicateBadgeIds.join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    }\n    // Check for potential hidden files or non-character files\n    const potentialHiddenFiles = allCharacterSlugs.filter((slug)=>slug.startsWith('.') || slug === 'index' || slug === 'README' || slug.includes('template'));\n    if (potentialHiddenFiles.length > 0) {\n        (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`Potential hidden/system files: ${potentialHiddenFiles.join(', ')}`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    }\n    (0,_debug_manager__WEBPACK_IMPORTED_MODULE_1__.debugLog)(`===== END DEBUG MISSING CHARACTER BADGES =====`, _debug_manager__WEBPACK_IMPORTED_MODULE_1__.DebugLevel.INFO);\n    return exactMissingSlugs.length > 0 ? exactMissingSlugs : missingSlugs;\n}\n/**\n * Check if user has access to a location slug (with fuzzy matching)\n */ function hasLocationAccess(slug) {\n    const badges = getLocationBadges();\n    // Use fuzzy matching for all badges\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, slug));\n}\n/**\n * Check if user has access to a series slug\n */ function hasSeriesAccess(slug) {\n    // Special case: horizons-hope is always accessible as the first series\n    if (slug === 'horizons-hope') {\n        return true;\n    }\n    const badges = getSeriesBadges();\n    // Use fuzzy matching for series badges\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, slug));\n}\n/**\n * Check if user has access to a story slug\n *\n * This function checks if a user has access to a story based on:\n * 1. If it's the Clone story (always accessible)\n * 2. If the user has the badge from the previous story in the series\n * 3. If the user has the specific badge for this story\n *\n * @param seriesSlug The series slug (e.g., 'horizons-hope')\n * @param storySlug The story slug (e.g., '02-corpie')\n */ function hasStoryAccess(seriesSlug, storySlug) {\n    // Handle undefined series slug\n    if (!seriesSlug) {\n        // Special cases for always accessible stories\n        if (storySlug === 'clone' || storySlug.includes('clone')) {\n            // For clone story, default to horizons-hope series\n            seriesSlug = 'horizons-hope';\n        } else if (storySlug === '00-introduction' || storySlug === 'introduction') {\n            // For introduction story, default to horizons-hope series\n            seriesSlug = 'horizons-hope';\n        } else {\n            // Check if the user has the specific badge for this story in any book\n            for (const bookSlug of BOOK_SLUGS){\n                const badges = getStoryBadgesFromCookie(bookSlug);\n                if (badges.some((badge)=>fuzzyMatchBadgeId(badge.id, storySlug))) {\n                    return true;\n                }\n            }\n            // If we can't determine the series, we can't check for sequential access\n            return false;\n        }\n    }\n    // Determine which book this series belongs to\n    const bookSlug = getBookSlugFromSeries(seriesSlug);\n    if (!bookSlug) {\n        return false;\n    }\n    // Special cases for always accessible stories\n    if (storySlug === 'clone' || storySlug.includes('clone')) {\n        return true;\n    }\n    // Special case for introduction story\n    if (storySlug === '00-introduction' || storySlug === 'introduction') {\n        return true;\n    }\n    // Get all story badges for this book\n    const badges = getStoryBadgesFromCookie(bookSlug);\n    // Check if user has the specific badge for this story\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, storySlug));\n}\n/**\n * Check if user has access to a technology slug\n */ function hasTechnologyAccess(slug) {\n    // No special cases - user must have the badge to access\n    const badges = getTechnologyBadges();\n    // Use fuzzy matching for technology badges\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, slug));\n}\n/**\n * Check if user has access to a theme slug\n */ function hasThemeAccess(slug) {\n    // No special cases - user must have the badge to access\n    const badges = getThemeBadges();\n    // Use fuzzy matching for theme badges\n    return badges.some((badge)=>fuzzyMatchBadgeId(badge.id, slug));\n}\n// This function is no longer needed as we have specific functions for each badge type\n/**\n * Reset all badges\n */ function resetAllBadges() {\n    try {\n        // Remove all badges using cookie managers\n        Object.values(BadgeType).forEach((type)=>{\n            if (type !== \"story\") {\n                cookieManagers[type].removeAll();\n            }\n        });\n        // Remove all story badges by book\n        BOOK_SLUGS.forEach((bookSlug)=>{\n            storyBadgeCookieManagers[bookSlug].removeAll();\n        });\n    } catch (e) {\n        console.error('Error removing badges from storage:', e);\n    }\n}\n/**\n * Add badges for a story with optional delay\n * @param storyData Story data containing series and badges\n * @param delayMs Optional delay in milliseconds before saving badges (default: 0)\n * @returns Object containing newly added badges\n */ function addBadgesForStory(storyData, delayMs = 0) {\n    const result = {\n        newCharacterBadges: [],\n        newLocationBadges: [],\n        newStoryBadges: [],\n        newSeriesBadge: null,\n        newSeriesBadges: [],\n        newTechnologyBadges: [],\n        newThemeBadges: []\n    };\n    // Add series badge if provided\n    if (!hasSeriesBadge(storyData.series)) {\n        // Format series badge name with proper capitalization\n        const badgeName = formatBadgeName(storyData.series);\n        const newSeriesBadge = {\n            id: storyData.series,\n            name: badgeName\n        };\n        // If delay is specified, use setTimeout to delay the badge saving\n        if (delayMs > 0) {\n            setTimeout(()=>{\n                addSeriesBadge(storyData.series);\n            }, delayMs);\n        } else {\n            // Otherwise save immediately\n            addSeriesBadge(storyData.series);\n        }\n        result.newSeriesBadge = newSeriesBadge;\n    }\n    // Add character badges if provided\n    if (storyData.characterBadges && storyData.characterBadges.length > 0) {\n        storyData.characterBadges.forEach((badgeId)=>{\n            if (!hasCharacterBadge(badgeId)) {\n                // Format character badge name with proper capitalization\n                const badgeName = formatBadgeName(badgeId);\n                const newCharacterBadge = {\n                    id: badgeId,\n                    name: badgeName\n                };\n                // If delay is specified, use setTimeout to delay the badge saving\n                if (delayMs > 0) {\n                    setTimeout(()=>{\n                        addCharacterBadge(badgeId);\n                    }, delayMs);\n                } else {\n                    // Otherwise save immediately\n                    addCharacterBadge(badgeId);\n                }\n                result.newCharacterBadges.push(newCharacterBadge);\n            }\n        });\n    }\n    // Add location badges if provided\n    if (storyData.locationBadges && storyData.locationBadges.length > 0) {\n        storyData.locationBadges.forEach((badgeId)=>{\n            if (!hasLocationBadge(badgeId)) {\n                // Format location badge name with proper capitalization\n                const badgeName = formatBadgeName(badgeId);\n                const newLocationBadge = {\n                    id: badgeId,\n                    name: badgeName\n                };\n                // If delay is specified, use setTimeout to delay the badge saving\n                if (delayMs > 0) {\n                    setTimeout(()=>{\n                        addLocationBadge(badgeId);\n                    }, delayMs);\n                } else {\n                    // Otherwise save immediately\n                    addLocationBadge(badgeId);\n                }\n                result.newLocationBadges.push(newLocationBadge);\n            }\n        });\n    }\n    // Add story badges if provided\n    if (storyData.storyBadges && storyData.storyBadges.length > 0) {\n        storyData.storyBadges.forEach((badgeId)=>{\n            if (!hasStoryBadge(storyData.series, badgeId)) {\n                // Format story badge name with proper capitalization\n                const badgeName = formatBadgeName(badgeId);\n                const newStoryBadge = {\n                    id: badgeId,\n                    name: badgeName\n                };\n                // If delay is specified, use setTimeout to delay the badge saving\n                if (delayMs > 0) {\n                    setTimeout(()=>{\n                        addStoryBadge(storyData.series, badgeId);\n                    }, delayMs);\n                } else {\n                    // Otherwise save immediately\n                    addStoryBadge(storyData.series, badgeId);\n                }\n                result.newStoryBadges.push(newStoryBadge);\n            }\n        });\n    }\n    // Add additional series badges if provided\n    if (storyData.seriesBadges && storyData.seriesBadges.length > 0) {\n        storyData.seriesBadges.forEach((badgeId)=>{\n            if (!hasSeriesBadge(badgeId)) {\n                // Format series badge name with proper capitalization\n                const badgeName = formatBadgeName(badgeId);\n                const newSeriesBadge = {\n                    id: badgeId,\n                    name: badgeName\n                };\n                // If delay is specified, use setTimeout to delay the badge saving\n                if (delayMs > 0) {\n                    setTimeout(()=>{\n                        addSeriesBadge(badgeId);\n                    }, delayMs);\n                } else {\n                    // Otherwise save immediately\n                    addSeriesBadge(badgeId);\n                }\n                result.newSeriesBadges.push(newSeriesBadge);\n            }\n        });\n    }\n    // Add technology badges if provided\n    if (storyData.technologyBadges && storyData.technologyBadges.length > 0) {\n        storyData.technologyBadges.forEach((badgeId)=>{\n            if (!hasTechnologyBadge(badgeId)) {\n                // Format technology badge name with proper capitalization\n                const badgeName = formatBadgeName(badgeId);\n                const newTechnologyBadge = {\n                    id: badgeId,\n                    name: badgeName\n                };\n                // If delay is specified, use setTimeout to delay the badge saving\n                if (delayMs > 0) {\n                    setTimeout(()=>{\n                        addTechnologyBadge(badgeId);\n                    }, delayMs);\n                } else {\n                    // Otherwise save immediately\n                    addTechnologyBadge(badgeId);\n                }\n                result.newTechnologyBadges.push(newTechnologyBadge);\n            }\n        });\n    }\n    // Add theme badges if provided\n    if (storyData.themeBadges && storyData.themeBadges.length > 0) {\n        storyData.themeBadges.forEach((badgeId)=>{\n            if (!hasThemeBadge(badgeId)) {\n                // Format theme badge name with proper capitalization\n                const badgeName = formatBadgeName(badgeId);\n                const newThemeBadge = {\n                    id: badgeId,\n                    name: badgeName\n                };\n                // If delay is specified, use setTimeout to delay the badge saving\n                if (delayMs > 0) {\n                    setTimeout(()=>{\n                        addThemeBadge(badgeId);\n                    }, delayMs);\n                } else {\n                    // Otherwise save immediately\n                    addThemeBadge(badgeId);\n                }\n                result.newThemeBadges.push(newThemeBadge);\n            }\n        });\n    }\n    return result;\n}\n/**\n * Check if user has any badges\n * This is used to determine if the user should have access to the paydata section\n */ function hasAnyBadge() {\n    const characterBadges = getCharacterBadges();\n    const locationBadges = getLocationBadges();\n    const technologyBadges = getTechnologyBadges();\n    const themeBadges = getThemeBadges();\n    // Check for story badges across all books\n    let hasStoryBadges = false;\n    for (const bookSlug of BOOK_SLUGS){\n        if (getStoryBadgesFromCookie(bookSlug).length > 0) {\n            hasStoryBadges = true;\n            break;\n        }\n    }\n    return characterBadges.length > 0 || locationBadges.length > 0 || technologyBadges.length > 0 || themeBadges.length > 0 || hasStoryBadges;\n}\n/**\n * @deprecated Use hasAnyBadge() instead\n */ function hasAnyCharacterOrLocationBadge() {\n    return hasAnyBadge();\n}\n/**\n * Format a badge name from a slug\n */ function formatBadgeName(slug) {\n    // Replace hyphens with spaces\n    const badgeName = slug.replace(/-/g, ' ');\n    // Capitalize the first letter of each word\n    return badgeName.split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./utils/access-badges.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./utils/cookie-manager.ts":
/*!*********************************!*\
  !*** ./utils/cookie-manager.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CookieManager: () => (/* binding */ CookieManager)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_0__]);\njs_cookie__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Cookie size limits\nconst MAX_COOKIE_SIZE = 3500; // Conservative limit (actual limit is ~4KB)\nconst COOKIE_EXPIRATION = 365; // 1 year\n/**\n * Robust cookie manager that automatically handles splitting and joining\n * data across multiple cookies when size limits are reached\n */ class CookieManager {\n    /**\n   * Create a new CookieManager\n   * @param baseKey The base key for the cookies\n   * @param maxCookieSize Maximum size for a single cookie (default: 3500 bytes)\n   * @param cookieExpiration Cookie expiration in days (default: 365 days)\n   */ constructor(baseKey, maxCookieSize = MAX_COOKIE_SIZE, cookieExpiration = COOKIE_EXPIRATION){\n        this.baseKey = baseKey;\n        this.maxCookieSize = maxCookieSize;\n        this.cookieExpiration = cookieExpiration;\n    }\n    /**\n   * Get the full key for a specific chunk\n   * @param chunk The chunk index (0 for the base key)\n   * @returns The full cookie key\n   */ getKeyForChunk(chunk) {\n        return chunk === 0 ? this.baseKey : `${this.baseKey}_${chunk}`;\n    }\n    /**\n   * Get all keys for this base key\n   * @returns Array of all keys\n   */ getAllKeys() {\n        const keys = [];\n        let chunk = 0;\n        let key = this.getKeyForChunk(chunk);\n        // Add the base key\n        keys.push(key);\n        // Check for additional chunks\n        chunk++;\n        key = this.getKeyForChunk(chunk);\n        while(js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(key) !== undefined){\n            keys.push(key);\n            chunk++;\n            key = this.getKeyForChunk(chunk);\n        }\n        return keys;\n    }\n    /**\n   * Set data in cookies, automatically splitting if needed\n   * @param data The data to store\n   */ set(data) {\n        try {\n            const jsonString = JSON.stringify(data);\n            // If data fits in a single cookie, just store it\n            if (jsonString.length <= this.maxCookieSize) {\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(this.baseKey, jsonString, {\n                    expires: this.cookieExpiration,\n                    sameSite: 'strict'\n                });\n                // Remove any existing chunk cookies\n                this.removeChunks();\n                return;\n            }\n            // Data is too large, need to split it\n            // For arrays, we can split the array into chunks\n            if (Array.isArray(data)) {\n                this.setArrayInChunks(data);\n            } else {\n                // For non-array data, we need to serialize and split the string\n                this.setStringInChunks(jsonString);\n            }\n        } catch  {}\n    }\n    /**\n   * Split an array into chunks and store in multiple cookies\n   * @param array The array to store\n   */ setArrayInChunks(array) {\n        try {\n            // Remove all existing cookies for this base key\n            this.removeAll();\n            // Calculate approximately how many items per chunk\n            // Start with a conservative estimate\n            let itemsPerChunk = Math.max(1, Math.floor(array.length / 5));\n            let chunk = 0;\n            let startIndex = 0;\n            while(startIndex < array.length){\n                // Adjust chunk size dynamically based on previous chunk size\n                if (chunk > 0) {\n                    const previousChunkSize = JSON.stringify(array.slice(startIndex - itemsPerChunk, startIndex)).length;\n                    const sizeRatio = this.maxCookieSize / previousChunkSize;\n                    // Adjust items per chunk, but ensure at least 1 item per chunk\n                    itemsPerChunk = Math.max(1, Math.floor(itemsPerChunk * sizeRatio * 0.9)); // 0.9 as safety factor\n                }\n                // Calculate end index for this chunk\n                const endIndex = Math.min(startIndex + itemsPerChunk, array.length);\n                const chunkArray = array.slice(startIndex, endIndex);\n                const chunkString = JSON.stringify(chunkArray);\n                // If this chunk is too big, reduce size and retry\n                if (chunkString.length > this.maxCookieSize && itemsPerChunk > 1) {\n                    itemsPerChunk = Math.max(1, Math.floor(itemsPerChunk * 0.7)); // Reduce by 30%\n                    continue;\n                }\n                // Store this chunk\n                const key = this.getKeyForChunk(chunk);\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(key, chunkString, {\n                    expires: this.cookieExpiration,\n                    sameSite: 'strict'\n                });\n                // Move to next chunk\n                startIndex = endIndex;\n                chunk++;\n            }\n            // Store metadata about the chunks\n            const metaKey = `${this.baseKey}_meta`;\n            const metadata = {\n                totalChunks: chunk,\n                totalItems: array.length,\n                isArray: true\n            };\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(metaKey, JSON.stringify(metadata), {\n                expires: this.cookieExpiration,\n                sameSite: 'strict'\n            });\n        } catch  {}\n    }\n    /**\n   * Split a string into chunks and store in multiple cookies\n   * @param jsonString The JSON string to store\n   */ setStringInChunks(jsonString) {\n        try {\n            // Remove all existing cookies for this base key\n            this.removeAll();\n            // Split the string into chunks\n            const totalChunks = Math.ceil(jsonString.length / this.maxCookieSize);\n            for(let i = 0; i < totalChunks; i++){\n                const start = i * this.maxCookieSize;\n                const end = Math.min(start + this.maxCookieSize, jsonString.length);\n                const chunk = jsonString.substring(start, end);\n                const key = this.getKeyForChunk(i);\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(key, chunk, {\n                    expires: this.cookieExpiration,\n                    sameSite: 'strict'\n                });\n            }\n            // Store metadata about the chunks\n            const metaKey = `${this.baseKey}_meta`;\n            const metadata = {\n                totalChunks,\n                totalLength: jsonString.length,\n                isArray: false\n            };\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(metaKey, JSON.stringify(metadata), {\n                expires: this.cookieExpiration,\n                sameSite: 'strict'\n            });\n        } catch  {}\n    }\n    /**\n   * Get data from cookies, automatically joining if split across multiple cookies\n   * @returns The stored data, or null if not found\n   */ get() {\n        try {\n            // Check if we have metadata indicating multiple chunks\n            const metaKey = `${this.baseKey}_meta`;\n            const metaString = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(metaKey);\n            if (metaString) {\n                // We have metadata, so data is split across multiple cookies\n                const metadata = JSON.parse(metaString);\n                if (metadata.isArray) {\n                    // Data is an array split across cookies\n                    return this.getArrayFromChunks(metadata.totalChunks);\n                } else {\n                    // Data is a string split across cookies\n                    return this.getStringFromChunks(metadata.totalChunks);\n                }\n            }\n            // No metadata, try to get from a single cookie\n            const data = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(this.baseKey);\n            if (!data) {\n                return null;\n            }\n            return JSON.parse(data);\n        } catch  {\n            return null;\n        }\n    }\n    /**\n   * Get an array that was split across multiple cookies\n   * @param totalChunks The total number of chunks\n   * @returns The combined array, or null if error\n   */ getArrayFromChunks(totalChunks) {\n        try {\n            const result = [];\n            for(let i = 0; i < totalChunks; i++){\n                const key = this.getKeyForChunk(i);\n                const chunkString = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(key);\n                if (!chunkString) {\n                    continue;\n                }\n                const chunkArray = JSON.parse(chunkString);\n                result.push(...chunkArray);\n            }\n            return result;\n        } catch  {\n            return null;\n        }\n    }\n    /**\n   * Get a string that was split across multiple cookies\n   * @param totalChunks The total number of chunks\n   * @returns The combined data, or null if error\n   */ getStringFromChunks(totalChunks) {\n        try {\n            let jsonString = '';\n            for(let i = 0; i < totalChunks; i++){\n                const key = this.getKeyForChunk(i);\n                const chunk = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(key);\n                if (!chunk) {\n                    continue;\n                }\n                jsonString += chunk;\n            }\n            return JSON.parse(jsonString);\n        } catch  {\n            return null;\n        }\n    }\n    /**\n   * Remove all cookies for this base key\n   */ removeAll() {\n        try {\n            // Get all keys\n            const keys = this.getAllKeys();\n            // Remove all cookies\n            keys.forEach((key)=>{\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(key);\n            });\n            // Remove metadata\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(`${this.baseKey}_meta`);\n        } catch  {}\n    }\n    /**\n   * Remove chunk cookies but keep the base cookie\n   */ removeChunks() {\n        try {\n            // Get all keys\n            const keys = this.getAllKeys();\n            // Remove chunk cookies (skip the base key)\n            keys.slice(1).forEach((key)=>{\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(key);\n            });\n            // Remove metadata\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(`${this.baseKey}_meta`);\n        } catch  {}\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./utils/cookie-manager.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./utils/debug-manager.ts":
/*!********************************!*\
  !*** ./utils/debug-manager.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BadgeDebugger: () => (/* binding */ BadgeDebugger),\n/* harmony export */   DebugLevel: () => (/* binding */ DebugLevel),\n/* harmony export */   debugLog: () => (/* binding */ debugLog),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getDebugState: () => (/* binding */ getDebugState),\n/* harmony export */   initDebugSettings: () => (/* binding */ initDebugSettings),\n/* harmony export */   setDebugEnabled: () => (/* binding */ setDebugEnabled),\n/* harmony export */   setDebugLevel: () => (/* binding */ setDebugLevel)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_0__]);\njs_cookie__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/**\n * Debug Manager for Horizon City Stories\n *\n * This utility provides debugging tools for the application,\n * with a focus on access badge management.\n */ \n// Debug state - disabled by default\nlet debugEnabled = false;\n// Debug levels\nvar DebugLevel = /*#__PURE__*/ function(DebugLevel) {\n    DebugLevel[DebugLevel[\"NONE\"] = 0] = \"NONE\";\n    DebugLevel[DebugLevel[\"ERROR\"] = 1] = \"ERROR\";\n    DebugLevel[DebugLevel[\"WARN\"] = 2] = \"WARN\";\n    DebugLevel[DebugLevel[\"INFO\"] = 3] = \"INFO\";\n    DebugLevel[DebugLevel[\"VERBOSE\"] = 4] = \"VERBOSE\";\n    return DebugLevel;\n}({});\n// Current debug level - set to NONE by default to disable all logging\nlet currentDebugLevel = 0;\n/**\n * Enable or disable debugging\n */ function setDebugEnabled(enabled) {\n    debugEnabled = enabled;\n    // Store in localStorage for persistence\n    if (false) {}\n}\n/**\n * Set the debug level\n */ function setDebugLevel(level) {\n    currentDebugLevel = level;\n    // Store in localStorage for persistence\n    if (false) {}\n}\n/**\n * Initialize debug settings from localStorage\n */ function initDebugSettings() {\n    if (false) {}\n}\n/**\n * Log a debug message if debugging is enabled\n */ function debugLog(message, level = 3, ...args) {\n    if (!debugEnabled || level > currentDebugLevel) return;\n    const _timestamp = new Date().toISOString();\n    const prefix = `[HCS Debug ${DebugLevel[level]}]`;\n    switch(level){\n        case 1:\n            console.error(`${prefix} ${message}`, ...args);\n            break;\n        case 2:\n            console.warn(`${prefix} ${message}`, ...args);\n            break;\n        case 3:\n            console.info(`${prefix} ${message}`, ...args);\n            break;\n        case 4:\n            console.debug(`${prefix} ${message}`, ...args);\n            break;\n    }\n}\n/**\n * Get the current debug state\n */ function getDebugState() {\n    return {\n        enabled: debugEnabled,\n        level: currentDebugLevel\n    };\n}\n/**\n * Debug utility for access badges\n */ const BadgeDebugger = {\n    /**\n   * Log all badges in storage\n   */ logAllBadges: ()=>{\n        if (!debugEnabled) return;\n        if (false) {}\n    },\n    /**\n   * Check if a badge exists\n   */ checkBadge: (id, type)=>{\n        if (false) {}\n        return false;\n    },\n    /**\n   * Add a badge directly to storage\n   */ addBadge: (id, type, name)=>{\n        if (false) {}\n    },\n    /**\n   * Remove a badge from storage\n   */ removeBadge: (id, type)=>{\n        if (false) {}\n    },\n    /**\n   * Clear all badges\n   */ clearAllBadges: ()=>{\n        if (false) {}\n    }\n};\n// Initialize debug settings on load - but keep disabled by default\nif (false) {}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    setDebugEnabled,\n    setDebugLevel,\n    debugLog,\n    getDebugState,\n    BadgeDebugger\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3V0aWxzL2RlYnVnLW1hbmFnZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFK0I7QUFXaEMsb0NBQW9DO0FBQ3BDLElBQUlDLGVBQWU7QUFFbkIsZUFBZTtBQUNSLHdDQUFLQzs7Ozs7O1dBQUFBO01BTVg7QUFFRCxzRUFBc0U7QUFDdEUsSUFBSUM7QUFFSjs7Q0FFQyxHQUNNLFNBQVNDLGdCQUFnQkMsT0FBZ0I7SUFDOUNKLGVBQWVJO0lBRWYsd0NBQXdDO0lBQ3hDLElBQUksS0FBNkIsRUFBRSxFQUVsQztBQUdIO0FBRUE7O0NBRUMsR0FDTSxTQUFTRyxjQUFjQyxLQUFpQjtJQUM3Q04sb0JBQW9CTTtJQUVwQix3Q0FBd0M7SUFDeEMsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0FBR0g7QUFFQTs7Q0FFQyxHQUNNLFNBQVNFO0lBQ2QsSUFBSSxLQUE2QixFQUFFLEVBZWxDO0FBQ0g7QUFFQTs7Q0FFQyxHQUNNLFNBQVNNLFNBQVNDLE9BQWUsRUFBRVQsU0FBbUMsRUFBRSxHQUFHVSxJQUFXO0lBQzNGLElBQUksQ0FBQ2xCLGdCQUFnQlEsUUFBUU4sbUJBQW1CO0lBRWhELE1BQU1pQixhQUFhLElBQUlDLE9BQU9DLFdBQVc7SUFDekMsTUFBTUMsU0FBUyxDQUFDLFdBQVcsRUFBRXJCLFVBQVUsQ0FBQ08sTUFBTSxDQUFDLENBQUMsQ0FBQztJQUVqRCxPQUFRQTtRQUNOO1lBQ0VlLFFBQVFDLEtBQUssQ0FBQyxHQUFHRixPQUFPLENBQUMsRUFBRUwsU0FBUyxLQUFLQztZQUN6QztRQUNGO1lBQ0VLLFFBQVFFLElBQUksQ0FBQyxHQUFHSCxPQUFPLENBQUMsRUFBRUwsU0FBUyxLQUFLQztZQUN4QztRQUNGO1lBQ0VLLFFBQVFHLElBQUksQ0FBQyxHQUFHSixPQUFPLENBQUMsRUFBRUwsU0FBUyxLQUFLQztZQUN4QztRQUNGO1lBQ0VLLFFBQVFJLEtBQUssQ0FBQyxHQUFHTCxPQUFPLENBQUMsRUFBRUwsU0FBUyxLQUFLQztZQUN6QztJQUNKO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNVO0lBQ2QsT0FBTztRQUNMeEIsU0FBU0o7UUFDVFEsT0FBT047SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxNQUFNMkIsZ0JBQWdCO0lBQzNCOztHQUVDLEdBQ0RDLGNBQWM7UUFDWixJQUFJLENBQUM5QixjQUFjO1FBRW5CLElBQUksS0FBNkIsRUFBRSxFQTZCbEM7SUFDSDtJQUVBOztHQUVDLEdBQ0RzQyxZQUFZLENBQUNDLElBQVlDO1FBQ3ZCLElBQUksS0FBNkIsRUFBRSxFQVlsQztRQUNELE9BQU87SUFDVDtJQUVBOztHQUVDLEdBQ0RNLFVBQVUsQ0FBQ1AsSUFBWUMsTUFBMkJPO1FBQ2hELElBQUksS0FBNkIsRUFBRSxFQTZDbEM7SUFDSDtJQUVBOztHQUVDLEdBQ0RTLGFBQWEsQ0FBQ2pCLElBQVlDO1FBQ3hCLElBQUksS0FBNkIsRUFBRSxFQTJCbEM7SUFDSDtJQUVBOztHQUVDLEdBQ0RvQixnQkFBZ0I7UUFDZCxJQUFJLEtBQTZCLEVBQUUsRUFhbEM7SUFDSDtBQUNGLEVBQUU7QUFFRixtRUFBbUU7QUFDbkUsSUFBSSxLQUE2QixFQUFFLEVBZWxDO0FBRUQsaUVBQWU7SUFDYnpEO0lBQ0FJO0lBQ0FTO0lBQ0FZO0lBQ0FDO0FBQ0YsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFRcXFByb2plY3RzXFxob3Jpem9uLWNpdHktc3Rvcmllc1xcdXRpbHNcXGRlYnVnLW1hbmFnZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEZWJ1ZyBNYW5hZ2VyIGZvciBIb3Jpem9uIENpdHkgU3Rvcmllc1xuICpcbiAqIFRoaXMgdXRpbGl0eSBwcm92aWRlcyBkZWJ1Z2dpbmcgdG9vbHMgZm9yIHRoZSBhcHBsaWNhdGlvbixcbiAqIHdpdGggYSBmb2N1cyBvbiBhY2Nlc3MgYmFkZ2UgbWFuYWdlbWVudC5cbiAqL1xuXG5pbXBvcnQgQ29va2llcyBmcm9tICdqcy1jb29raWUnO1xuaW1wb3J0IHsgQmFkZ2VUeXBlIH0gZnJvbSAnLi9hY2Nlc3MtYmFkZ2VzJztcblxuLy8gSW50ZXJmYWNlIGZvciBiYWRnZXMgc3RvcmVkIGluIGxvY2FsU3RvcmFnZSBhbmQgY29va2llc1xuaW50ZXJmYWNlIFN0b3JlZEJhZGdlIHtcbiAgaWQ6IHN0cmluZztcbiAgdHlwZTogQmFkZ2VUeXBlIHwgJ3N0b3J5JztcbiAgbmFtZTogc3RyaW5nO1xuICBkYXRlVW5sb2NrZWQ/OiBzdHJpbmc7XG59XG5cbi8vIERlYnVnIHN0YXRlIC0gZGlzYWJsZWQgYnkgZGVmYXVsdFxubGV0IGRlYnVnRW5hYmxlZCA9IGZhbHNlO1xuXG4vLyBEZWJ1ZyBsZXZlbHNcbmV4cG9ydCBlbnVtIERlYnVnTGV2ZWwge1xuICBOT05FID0gMCxcbiAgRVJST1IgPSAxLFxuICBXQVJOID0gMixcbiAgSU5GTyA9IDMsXG4gIFZFUkJPU0UgPSA0XG59XG5cbi8vIEN1cnJlbnQgZGVidWcgbGV2ZWwgLSBzZXQgdG8gTk9ORSBieSBkZWZhdWx0IHRvIGRpc2FibGUgYWxsIGxvZ2dpbmdcbmxldCBjdXJyZW50RGVidWdMZXZlbCA9IERlYnVnTGV2ZWwuTk9ORTtcblxuLyoqXG4gKiBFbmFibGUgb3IgZGlzYWJsZSBkZWJ1Z2dpbmdcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNldERlYnVnRW5hYmxlZChlbmFibGVkOiBib29sZWFuKTogdm9pZCB7XG4gIGRlYnVnRW5hYmxlZCA9IGVuYWJsZWQ7XG5cbiAgLy8gU3RvcmUgaW4gbG9jYWxTdG9yYWdlIGZvciBwZXJzaXN0ZW5jZVxuICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnaG9yaXpvbl9kZWJ1Z19lbmFibGVkJywgZW5hYmxlZCA/ICd0cnVlJyA6ICdmYWxzZScpO1xuICB9XG5cbiAgXG59XG5cbi8qKlxuICogU2V0IHRoZSBkZWJ1ZyBsZXZlbFxuICovXG5leHBvcnQgZnVuY3Rpb24gc2V0RGVidWdMZXZlbChsZXZlbDogRGVidWdMZXZlbCk6IHZvaWQge1xuICBjdXJyZW50RGVidWdMZXZlbCA9IGxldmVsO1xuXG4gIC8vIFN0b3JlIGluIGxvY2FsU3RvcmFnZSBmb3IgcGVyc2lzdGVuY2VcbiAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2hvcml6b25fZGVidWdfbGV2ZWwnLCBsZXZlbC50b1N0cmluZygpKTtcbiAgfVxuXG4gIFxufVxuXG4vKipcbiAqIEluaXRpYWxpemUgZGVidWcgc2V0dGluZ3MgZnJvbSBsb2NhbFN0b3JhZ2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGluaXREZWJ1Z1NldHRpbmdzKCk6IHZvaWQge1xuICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAvLyBMb2FkIGRlYnVnIGVuYWJsZWQgc3RhdGVcbiAgICBjb25zdCBkZWJ1Z0VuYWJsZWRTdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaG9yaXpvbl9kZWJ1Z19lbmFibGVkJyk7XG4gICAgaWYgKGRlYnVnRW5hYmxlZFN0cikge1xuICAgICAgZGVidWdFbmFibGVkID0gZGVidWdFbmFibGVkU3RyID09PSAndHJ1ZSc7XG4gICAgfVxuXG4gICAgLy8gTG9hZCBkZWJ1ZyBsZXZlbFxuICAgIGNvbnN0IGRlYnVnTGV2ZWxTdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaG9yaXpvbl9kZWJ1Z19sZXZlbCcpO1xuICAgIGlmIChkZWJ1Z0xldmVsU3RyKSB7XG4gICAgICBjb25zdCBsZXZlbCA9IHBhcnNlSW50KGRlYnVnTGV2ZWxTdHIpO1xuICAgICAgaWYgKCFpc05hTihsZXZlbCkgJiYgbGV2ZWwgPj0gMCAmJiBsZXZlbCA8PSA0KSB7XG4gICAgICAgIGN1cnJlbnREZWJ1Z0xldmVsID0gbGV2ZWw7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogTG9nIGEgZGVidWcgbWVzc2FnZSBpZiBkZWJ1Z2dpbmcgaXMgZW5hYmxlZFxuICovXG5leHBvcnQgZnVuY3Rpb24gZGVidWdMb2cobWVzc2FnZTogc3RyaW5nLCBsZXZlbDogRGVidWdMZXZlbCA9IERlYnVnTGV2ZWwuSU5GTywgLi4uYXJnczogYW55W10pOiB2b2lkIHtcbiAgaWYgKCFkZWJ1Z0VuYWJsZWQgfHwgbGV2ZWwgPiBjdXJyZW50RGVidWdMZXZlbCkgcmV0dXJuO1xuXG4gIGNvbnN0IF90aW1lc3RhbXAgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG4gIGNvbnN0IHByZWZpeCA9IGBbSENTIERlYnVnICR7RGVidWdMZXZlbFtsZXZlbF19XWA7XG5cbiAgc3dpdGNoIChsZXZlbCkge1xuICAgIGNhc2UgRGVidWdMZXZlbC5FUlJPUjpcbiAgICAgIGNvbnNvbGUuZXJyb3IoYCR7cHJlZml4fSAke21lc3NhZ2V9YCwgLi4uYXJncyk7XG4gICAgICBicmVhaztcbiAgICBjYXNlIERlYnVnTGV2ZWwuV0FSTjpcbiAgICAgIGNvbnNvbGUud2FybihgJHtwcmVmaXh9ICR7bWVzc2FnZX1gLCAuLi5hcmdzKTtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgRGVidWdMZXZlbC5JTkZPOlxuICAgICAgY29uc29sZS5pbmZvKGAke3ByZWZpeH0gJHttZXNzYWdlfWAsIC4uLmFyZ3MpO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSBEZWJ1Z0xldmVsLlZFUkJPU0U6XG4gICAgICBjb25zb2xlLmRlYnVnKGAke3ByZWZpeH0gJHttZXNzYWdlfWAsIC4uLmFyZ3MpO1xuICAgICAgYnJlYWs7XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgdGhlIGN1cnJlbnQgZGVidWcgc3RhdGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldERlYnVnU3RhdGUoKTogeyBlbmFibGVkOiBib29sZWFuLCBsZXZlbDogRGVidWdMZXZlbCB9IHtcbiAgcmV0dXJuIHtcbiAgICBlbmFibGVkOiBkZWJ1Z0VuYWJsZWQsXG4gICAgbGV2ZWw6IGN1cnJlbnREZWJ1Z0xldmVsXG4gIH07XG59XG5cbi8qKlxuICogRGVidWcgdXRpbGl0eSBmb3IgYWNjZXNzIGJhZGdlc1xuICovXG5leHBvcnQgY29uc3QgQmFkZ2VEZWJ1Z2dlciA9IHtcbiAgLyoqXG4gICAqIExvZyBhbGwgYmFkZ2VzIGluIHN0b3JhZ2VcbiAgICovXG4gIGxvZ0FsbEJhZGdlczogKCk6IHZvaWQgPT4ge1xuICAgIGlmICghZGVidWdFbmFibGVkKSByZXR1cm47XG5cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIC8vIENoZWNrIGJvdGggbG9jYWxTdG9yYWdlIGFuZCBjb29raWVzXG4gICAgICBjb25zdCBsb2NhbFN0b3JhZ2VCYWRnZXNTdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJyk7XG4gICAgICBjb25zdCBjb29raWVCYWRnZXNTdHIgPSBDb29raWVzLmdldCgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJyk7XG5cbiAgICAgIFxuICAgICAgXG5cbiAgICAgIGlmIChsb2NhbFN0b3JhZ2VCYWRnZXNTdHIpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBfYmFkZ2VzOiBTdG9yZWRCYWRnZVtdID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2VCYWRnZXNTdHIpO1xuICAgICAgICAgIFxuICAgICAgICB9IGNhdGNoIChfZSkge1xuICAgICAgICAgIFxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBcbiAgICAgIH1cblxuICAgICAgaWYgKGNvb2tpZUJhZGdlc1N0cikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IF9iYWRnZXM6IFN0b3JlZEJhZGdlW10gPSBKU09OLnBhcnNlKGNvb2tpZUJhZGdlc1N0cik7XG4gICAgICAgICAgXG4gICAgICAgIH0gY2F0Y2ggKF9lKSB7XG4gICAgICAgICAgXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIFxuICAgICAgfVxuICAgIH1cbiAgfSxcblxuICAvKipcbiAgICogQ2hlY2sgaWYgYSBiYWRnZSBleGlzdHNcbiAgICovXG4gIGNoZWNrQmFkZ2U6IChpZDogc3RyaW5nLCB0eXBlOiBCYWRnZVR5cGUgfCAnc3RvcnknKTogYm9vbGVhbiA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCBiYWRnZXNTdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJyk7XG4gICAgICBpZiAoYmFkZ2VzU3RyKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgYmFkZ2VzOiBTdG9yZWRCYWRnZVtdID0gSlNPTi5wYXJzZShiYWRnZXNTdHIpO1xuICAgICAgICAgIGNvbnN0IGV4aXN0cyA9IGJhZGdlcy5zb21lKGJhZGdlID0+IGJhZGdlLmlkID09PSBpZCAmJiBiYWRnZS50eXBlID09PSB0eXBlKTtcbiAgICAgICAgICBcbiAgICAgICAgICByZXR1cm4gZXhpc3RzO1xuICAgICAgICB9IGNhdGNoIChfZSkge1xuICAgICAgICAgIFxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfSxcblxuICAvKipcbiAgICogQWRkIGEgYmFkZ2UgZGlyZWN0bHkgdG8gc3RvcmFnZVxuICAgKi9cbiAgYWRkQmFkZ2U6IChpZDogc3RyaW5nLCB0eXBlOiBCYWRnZVR5cGUgfCAnc3RvcnknLCBuYW1lOiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIEdldCBjdXJyZW50IGJhZGdlcyBmcm9tIGJvdGggbG9jYWxTdG9yYWdlIGFuZCBjb29raWVzXG4gICAgICAgIGxldCBiYWRnZXM6IFN0b3JlZEJhZGdlW10gPSBbXTtcbiAgICAgICAgY29uc3QgbG9jYWxTdG9yYWdlQmFkZ2VzU3RyID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2hvcml6b25fYWNjZXNzX2JhZGdlcycpO1xuICAgICAgICBjb25zdCBjb29raWVCYWRnZXNTdHIgPSBDb29raWVzLmdldCgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJyk7XG5cbiAgICAgICAgLy8gUHJlZmVyIGxvY2FsU3RvcmFnZSBiYWRnZXMgaWYgYXZhaWxhYmxlXG4gICAgICAgIGlmIChsb2NhbFN0b3JhZ2VCYWRnZXNTdHIpIHtcbiAgICAgICAgICBiYWRnZXMgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZUJhZGdlc1N0cik7XG4gICAgICAgIH0gZWxzZSBpZiAoY29va2llQmFkZ2VzU3RyKSB7XG4gICAgICAgICAgYmFkZ2VzID0gSlNPTi5wYXJzZShjb29raWVCYWRnZXNTdHIpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ2hlY2sgaWYgYmFkZ2UgYWxyZWFkeSBleGlzdHNcbiAgICAgICAgY29uc3QgYmFkZ2VFeGlzdHMgPSBiYWRnZXMuc29tZShcbiAgICAgICAgICBiYWRnZSA9PiBiYWRnZS5pZCA9PT0gaWQgJiYgYmFkZ2UudHlwZSA9PT0gdHlwZVxuICAgICAgICApO1xuXG4gICAgICAgIGlmICghYmFkZ2VFeGlzdHMpIHtcbiAgICAgICAgICAvLyBBZGQgbmV3IGJhZGdlXG4gICAgICAgICAgYmFkZ2VzLnB1c2goe1xuICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICB0eXBlLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIGRhdGVVbmxvY2tlZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICAvLyBDb252ZXJ0IHRvIEpTT04gc3RyaW5nXG4gICAgICAgICAgY29uc3QgYmFkZ2VzU3RyaW5nID0gSlNPTi5zdHJpbmdpZnkoYmFkZ2VzKTtcblxuICAgICAgICAgIC8vIFNhdmUgdG8gYm90aCBsb2NhbFN0b3JhZ2UgYW5kIGNvb2tpZXNcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJywgYmFkZ2VzU3RyaW5nKTtcbiAgICAgICAgICBDb29raWVzLnNldCgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJywgYmFkZ2VzU3RyaW5nLCB7XG4gICAgICAgICAgICBleHBpcmVzOiAzNjUsIC8vIDEgeWVhclxuICAgICAgICAgICAgc2FtZVNpdGU6ICdzdHJpY3QnXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoX2UpIHtcbiAgICAgICAgXG4gICAgICB9XG4gICAgfVxuICB9LFxuXG4gIC8qKlxuICAgKiBSZW1vdmUgYSBiYWRnZSBmcm9tIHN0b3JhZ2VcbiAgICovXG4gIHJlbW92ZUJhZGdlOiAoaWQ6IHN0cmluZywgdHlwZTogQmFkZ2VUeXBlIHwgJ3N0b3J5Jyk6IHZvaWQgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gR2V0IGN1cnJlbnQgYmFkZ2VzIGZyb20gbG9jYWxTdG9yYWdlXG4gICAgICAgIGNvbnN0IGJhZGdlc1N0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdob3Jpem9uX2FjY2Vzc19iYWRnZXMnKTtcbiAgICAgICAgaWYgKGJhZGdlc1N0cikge1xuICAgICAgICAgIGxldCBiYWRnZXM6IFN0b3JlZEJhZGdlW10gPSBKU09OLnBhcnNlKGJhZGdlc1N0cik7XG5cbiAgICAgICAgICAvLyBGaWx0ZXIgb3V0IHRoZSBiYWRnZVxuICAgICAgICAgIGNvbnN0IGluaXRpYWxDb3VudCA9IGJhZGdlcy5sZW5ndGg7XG4gICAgICAgICAgYmFkZ2VzID0gYmFkZ2VzLmZpbHRlcihiYWRnZSA9PiAhKGJhZGdlLmlkID09PSBpZCAmJiBiYWRnZS50eXBlID09PSB0eXBlKSk7XG5cbiAgICAgICAgICAvLyBTYXZlIGJhY2sgdG8gYm90aCBsb2NhbFN0b3JhZ2UgYW5kIGNvb2tpZXMgaWYgY2hhbmdlZFxuICAgICAgICAgIGlmIChiYWRnZXMubGVuZ3RoICE9PSBpbml0aWFsQ291bnQpIHtcbiAgICAgICAgICAgIGNvbnN0IGJhZGdlc1N0cmluZyA9IEpTT04uc3RyaW5naWZ5KGJhZGdlcyk7XG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJywgYmFkZ2VzU3RyaW5nKTtcbiAgICAgICAgICAgIENvb2tpZXMuc2V0KCdob3Jpem9uX2FjY2Vzc19iYWRnZXMnLCBiYWRnZXNTdHJpbmcsIHtcbiAgICAgICAgICAgICAgZXhwaXJlczogMzY1LCAvLyAxIHllYXJcbiAgICAgICAgICAgICAgc2FtZVNpdGU6ICdzdHJpY3QnXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIFxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKF9lKSB7XG4gICAgICAgIFxuICAgICAgfVxuICAgIH1cbiAgfSxcblxuICAvKipcbiAgICogQ2xlYXIgYWxsIGJhZGdlc1xuICAgKi9cbiAgY2xlYXJBbGxCYWRnZXM6ICgpOiB2b2lkID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIC8vIEltcG9ydCB0aGUgbmVjZXNzYXJ5IGZ1bmN0aW9ucyB0byBjbGVhciBiYWRnZXNcbiAgICAgIGltcG9ydCgnLi9hY2Nlc3MtYmFkZ2VzJykudGhlbigoeyByZXNldEFsbEJhZGdlcyB9KSA9PiB7XG4gICAgICAgIC8vIFVzZSB0aGUgcmVzZXRBbGxCYWRnZXMgZnVuY3Rpb24gdG8gY2xlYXIgYWxsIHR5cGUtc3BlY2lmaWMgY29va2llc1xuICAgICAgICByZXNldEFsbEJhZGdlcygpO1xuXG4gICAgICAgIC8vIEFsc28gY2xlYXIgdGhlIGxlZ2FjeSBjb29raWUgZm9yIGdvb2QgbWVhc3VyZVxuICAgICAgICBDb29raWVzLnJlbW92ZSgnaG9yaXpvbl9hY2Nlc3NfYmFkZ2VzJyk7XG5cbiAgICAgICAgXG4gICAgICB9KS5jYXRjaChfZXJyb3IgPT4ge1xuICAgICAgICBcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxufTtcblxuLy8gSW5pdGlhbGl6ZSBkZWJ1ZyBzZXR0aW5ncyBvbiBsb2FkIC0gYnV0IGtlZXAgZGlzYWJsZWQgYnkgZGVmYXVsdFxuaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gIC8vIEluaXRpYWxpemUgb24gbmV4dCB0aWNrIHRvIGVuc3VyZSB3aW5kb3cgaXMgYXZhaWxhYmxlXG4gIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgIC8vIEZvcmNlIGRlYnVnIHRvIGJlIGRpc2FibGVkIGJ5IGRlZmF1bHRcbiAgICBkZWJ1Z0VuYWJsZWQgPSBmYWxzZTtcbiAgICBjdXJyZW50RGVidWdMZXZlbCA9IERlYnVnTGV2ZWwuTk9ORTtcblxuICAgIC8vIEFkZCB0byB3aW5kb3cgZm9yIGNvbnNvbGUgYWNjZXNzIChvbmx5IGZvciBkZXZlbG9wbWVudClcbiAgICAod2luZG93IGFzIGFueSkuSG9yaXpvbkRlYnVnID0ge1xuICAgICAgZW5hYmxlOiBzZXREZWJ1Z0VuYWJsZWQsXG4gICAgICBzZXRMZXZlbDogc2V0RGVidWdMZXZlbCxcbiAgICAgIGdldFN0YXRlOiBnZXREZWJ1Z1N0YXRlLFxuICAgICAgYmFkZ2VzOiBCYWRnZURlYnVnZ2VyXG4gICAgfTtcbiAgfSwgMCk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgc2V0RGVidWdFbmFibGVkLFxuICBzZXREZWJ1Z0xldmVsLFxuICBkZWJ1Z0xvZyxcbiAgZ2V0RGVidWdTdGF0ZSxcbiAgQmFkZ2VEZWJ1Z2dlclxufTtcbiJdLCJuYW1lcyI6WyJDb29raWVzIiwiZGVidWdFbmFibGVkIiwiRGVidWdMZXZlbCIsImN1cnJlbnREZWJ1Z0xldmVsIiwic2V0RGVidWdFbmFibGVkIiwiZW5hYmxlZCIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJzZXREZWJ1Z0xldmVsIiwibGV2ZWwiLCJ0b1N0cmluZyIsImluaXREZWJ1Z1NldHRpbmdzIiwiZGVidWdFbmFibGVkU3RyIiwiZ2V0SXRlbSIsImRlYnVnTGV2ZWxTdHIiLCJwYXJzZUludCIsImlzTmFOIiwiZGVidWdMb2ciLCJtZXNzYWdlIiwiYXJncyIsIl90aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJwcmVmaXgiLCJjb25zb2xlIiwiZXJyb3IiLCJ3YXJuIiwiaW5mbyIsImRlYnVnIiwiZ2V0RGVidWdTdGF0ZSIsIkJhZGdlRGVidWdnZXIiLCJsb2dBbGxCYWRnZXMiLCJsb2NhbFN0b3JhZ2VCYWRnZXNTdHIiLCJjb29raWVCYWRnZXNTdHIiLCJnZXQiLCJfYmFkZ2VzIiwiSlNPTiIsInBhcnNlIiwiX2UiLCJjaGVja0JhZGdlIiwiaWQiLCJ0eXBlIiwiYmFkZ2VzU3RyIiwiYmFkZ2VzIiwiZXhpc3RzIiwic29tZSIsImJhZGdlIiwiYWRkQmFkZ2UiLCJuYW1lIiwiYmFkZ2VFeGlzdHMiLCJwdXNoIiwiZGF0ZVVubG9ja2VkIiwiYmFkZ2VzU3RyaW5nIiwic3RyaW5naWZ5Iiwic2V0IiwiZXhwaXJlcyIsInNhbWVTaXRlIiwicmVtb3ZlQmFkZ2UiLCJpbml0aWFsQ291bnQiLCJsZW5ndGgiLCJmaWx0ZXIiLCJjbGVhckFsbEJhZGdlcyIsInRoZW4iLCJyZXNldEFsbEJhZGdlcyIsInJlbW92ZSIsImNhdGNoIiwiX2Vycm9yIiwic2V0VGltZW91dCIsIndpbmRvdyIsIkhvcml6b25EZWJ1ZyIsImVuYWJsZSIsInNldExldmVsIiwiZ2V0U3RhdGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./utils/debug-manager.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./utils/restriction-manager.ts":
/*!**************************************!*\
  !*** ./utils/restriction-manager.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyRestrictionState: () => (/* binding */ applyRestrictionState),\n/* harmony export */   checkAndApplyRestriction: () => (/* binding */ checkAndApplyRestriction),\n/* harmony export */   createRestrictionObserver: () => (/* binding */ createRestrictionObserver),\n/* harmony export */   hasAccess: () => (/* binding */ hasAccess),\n/* harmony export */   initRestrictionSystem: () => (/* binding */ initRestrictionSystem),\n/* harmony export */   initializeOnLoad: () => (/* binding */ initializeOnLoad)\n/* harmony export */ });\n/* harmony import */ var _access_badge_manager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./access-badge-manager */ \"(pages-dir-node)/./utils/access-badge-manager.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__]);\n_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/**\n * Restriction Manager\n *\n * A unified utility for managing content restrictions based on access badges.\n * This file provides functions to check access and apply restriction styles.\n */ // Add TypeScript declaration for the global image tracker\n\n/**\n * Check if a user has access to specific content\n *\n * @param type The type of content to check\n * @param slug The slug of the content\n * @returns True if the user has access, false otherwise\n */ function hasAccess(type, slug) {\n    switch(type){\n        case 'character':\n            return (0,_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__.hasCharacterAccess)(slug);\n        case 'location':\n            return (0,_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__.hasLocationAccess)(slug);\n        case 'story':\n            // For story access, we need to determine the series from the slug\n            // The slug format is expected to be 'series/story'\n            if (slug.includes('/')) {\n                const [series, story] = slug.split('/');\n                return (0,_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__.hasStoryAccess)(series, story);\n            }\n            // If no series is provided, default to horizons-hope for backward compatibility\n            return (0,_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__.hasStoryAccess)('horizons-hope', slug);\n        case 'series':\n            return (0,_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__.hasSeriesAccess)(slug);\n        case 'technology':\n            return (0,_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__.hasTechnologyAccess)(slug);\n        case 'theme':\n            return (0,_access_badge_manager__WEBPACK_IMPORTED_MODULE_0__.hasThemeAccess)(slug);\n        // Always grant access to home, paydata, outreach, and blog pages\n        case 'home':\n        case 'paydata':\n        case 'outreach':\n        case 'blog':\n            return true;\n        default:\n            return false;\n    }\n}\n/**\n * Initialize the restriction system by adding the CSS file to the document\n * This should be called once when the app starts\n */ function initRestrictionSystem() {\n    if (typeof document === 'undefined') return;\n    // Check if the stylesheet is already added\n    if (!document.getElementById('content-restriction-styles')) {\n        const link = document.createElement('link');\n        link.id = 'content-restriction-styles';\n        link.rel = 'stylesheet';\n        link.href = '/styles/content-restriction.css';\n        document.head.appendChild(link);\n    }\n}\n/**\n * Apply restriction state to an element based on access\n *\n * @param element The DOM element to apply restrictions to\n * @param hasAccess Whether the user has access to this content\n */ function applyRestrictionState(element, hasAccess) {\n    if (!element) return;\n    // Set the data-restricted attribute based on access\n    element.setAttribute('data-restricted', hasAccess ? 'false' : 'true');\n}\n/**\n * Check and apply restriction state to an element\n *\n * @param element The DOM element to apply restrictions to\n * @param type The type of content\n * @param slug The slug of the content\n * @returns True if the user has access, false otherwise\n */ function checkAndApplyRestriction(element, type, slug) {\n    const userHasAccess = hasAccess(type, slug);\n    applyRestrictionState(element, userHasAccess);\n    return userHasAccess;\n}\n/**\n * Create a restriction observer that watches for elements with data-restrict attributes\n * and automatically applies restriction states\n */ function createRestrictionObserver() {\n    if (true) return;\n    // Function to process elements with data-restrict attributes\n    const processRestrictedElements = ()=>{\n        const elements = document.querySelectorAll('[data-restrict]');\n        elements.forEach((element)=>{\n            const el = element;\n            const restrictData = el.dataset.restrict;\n            if (!restrictData) return;\n            try {\n                // Parse the restriction data (format: \"type:slug\")\n                const [type, slug] = restrictData.split(':');\n                if (!type || !slug) return;\n                // Check and apply restriction\n                checkAndApplyRestriction(el, type, slug);\n            } catch (error) {\n                console.error('Error processing restriction data:', error);\n            }\n        });\n    };\n    // Process existing elements\n    processRestrictedElements();\n    // Create observer to watch for new elements\n    const observer = new MutationObserver((mutations)=>{\n        let shouldProcess = false;\n        // Check if any mutations added elements with data-restrict\n        mutations.forEach((mutation)=>{\n            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {\n                for(let i = 0; i < mutation.addedNodes.length; i++){\n                    const node = mutation.addedNodes[i];\n                    if (node.nodeType === Node.ELEMENT_NODE) {\n                        const el = node;\n                        if (el.hasAttribute('data-restrict') || el.querySelector('[data-restrict]')) {\n                            shouldProcess = true;\n                            break;\n                        }\n                    }\n                }\n            } else if (mutation.type === 'attributes' && mutation.attributeName === 'data-restrict') {\n                shouldProcess = true;\n            }\n        });\n        if (shouldProcess) {\n            processRestrictedElements();\n        }\n    });\n    // Start observing\n    observer.observe(document.body, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: [\n            'data-restrict'\n        ]\n    });\n}\n/**\n * Initialize the restriction system when the document is ready\n */ function initializeOnLoad() {\n    if (false) {}\n}\n// Helper function to process all restricted elements\nfunction processRestrictedElements() {\n    if (typeof document === 'undefined') return;\n    const elements = document.querySelectorAll('[data-restrict]');\n    elements.forEach((element)=>{\n        const el = element;\n        const restrictData = el.dataset.restrict;\n        if (!restrictData) return;\n        try {\n            // Parse the restriction data (format: \"type:slug\")\n            const [type, slug] = restrictData.split(':');\n            if (!type || !slug) return;\n            // Check and apply restriction\n            checkAndApplyRestriction(el, type, slug);\n        } catch (error) {\n            console.error('Error processing restriction data:', error);\n        }\n    });\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./utils/restriction-manager.ts\n");

/***/ }),

/***/ "@chakra-ui/react":
/*!***********************************!*\
  !*** external "@chakra-ui/react" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ "@chakra-ui/react/preset":
/*!******************************************!*\
  !*** external "@chakra-ui/react/preset" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@chakra-ui/react/preset");;

/***/ }),

/***/ "@vercel/analytics/react":
/*!******************************************!*\
  !*** external "@vercel/analytics/react" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@vercel/analytics/react");;

/***/ }),

/***/ "@vercel/speed-insights/next":
/*!**********************************************!*\
  !*** external "@vercel/speed-insights/next" ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@vercel/speed-insights/next");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "js-cookie":
/*!****************************!*\
  !*** external "js-cookie" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@fontsource"], () => (__webpack_exec__("(pages-dir-node)/./pages/_app.tsx")));
module.exports = __webpack_exports__;

})();