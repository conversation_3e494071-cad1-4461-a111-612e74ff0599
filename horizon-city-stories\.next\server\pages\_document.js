"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_1___default()) {\n    static async getInitialProps(ctx) {\n        const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(ctx);\n        // Add cache control headers to all pages\n        if (ctx.res) {\n            // Set cache control headers for all pages\n            ctx.res.setHeader('Cache-Control', 'public, max-age=172800, s-maxage=172800');\n            // Add Vary header to ensure proper cache separation based on cookies\n            ctx.res.setHeader('Vary', 'Cookie');\n            // Add a custom header to indicate this is a statically generated page\n            ctx.res.setHeader('X-Page-Generation', 'ISR');\n        }\n        return initialProps;\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n            lang: \"en\",\n            style: {\n                backgroundColor: '#000000'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            charSet: \"utf-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1.0, maximum-scale=5.0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://www.horizon-city.com\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"dns-prefetch\",\n                            href: \"https://www.horizon-city.com\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\pages\\\\_document.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyDocument);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(pages-dir-node)/./pages/_document.tsx")));
module.exports = __webpack_exports__;

})();