"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/blog/process-post";
exports.ids = ["pages/api/blog/process-post"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fprocess-post&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cprocess-post.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fprocess-post&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cprocess-post.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_blog_process_post_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\blog\\process-post.ts */ \"(api-node)/./pages/api/blog/process-post.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_blog_process_post_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_blog_process_post_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/blog/process-post\",\n        pathname: \"/api/blog/process-post\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_blog_process_post_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fprocess-post&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cprocess-post.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/blog/process-post.ts":
/*!****************************************!*\
  !*** ./pages/api/blog/process-post.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function handler(req, res) {\n    // Only allow POST method\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            message: 'Method not allowed'\n        });\n    }\n    // Only allow in development mode\n    if (false) {}\n    try {\n        const { slug, force = false } = req.body;\n        if (!slug) {\n            return res.status(400).json({\n                message: 'Slug is required'\n            });\n        }\n        console.log(`Processing blog post: ${slug} (force=${force})`);\n        // Set up args for the process-blog-post.ts script\n        const tsNodePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'node_modules', '.bin', 'ts-node');\n        const scriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'scripts', 'blog', 'process-blog-post.ts');\n        const args = [\n            scriptPath,\n            slug\n        ];\n        if (force) {\n            args.push('--force');\n        }\n        console.log(`Executing: ${tsNodePath} ${args.join(' ')}`);\n        // Create a child process to run the script\n        const scriptProcess = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)(tsNodePath, args, {\n            cwd: process.cwd(),\n            stdio: 'pipe',\n            shell: true\n        });\n        let stdout = '';\n        let stderr = '';\n        // Collect stdout and log it in real-time\n        scriptProcess.stdout.on('data', (data)=>{\n            const dataStr = data.toString();\n            stdout += dataStr;\n            console.log(`[Process ${slug}]: ${dataStr}`);\n        });\n        // Collect stderr and log it in real-time\n        scriptProcess.stderr.on('data', (data)=>{\n            const dataStr = data.toString();\n            stderr += dataStr;\n            console.error(`[Process ${slug} ERROR]: ${dataStr}`);\n        });\n        // Handle process completion\n        return new Promise((resolve)=>{\n            // Set a timeout in case the process hangs\n            const timeout = setTimeout(()=>{\n                scriptProcess.kill();\n                console.error(`Process timed out after 5 minutes for ${slug}`);\n                res.status(500).json({\n                    message: 'Blog post processing timed out after 5 minutes',\n                    slug,\n                    partialOutput: stdout\n                });\n                resolve();\n            }, 5 * 60 * 1000); // 5 minutes timeout\n            scriptProcess.on('close', (code)=>{\n                clearTimeout(timeout);\n                console.log(`Process for ${slug} completed with code ${code}`);\n                if (code === 0) {\n                    res.status(200).json({\n                        message: 'Blog post processed successfully',\n                        output: stdout.substring(0, 1000) + (stdout.length > 1000 ? '...(truncated)' : ''),\n                        slug\n                    });\n                } else {\n                    res.status(500).json({\n                        message: 'Error processing blog post',\n                        error: stderr.substring(0, 1000) + (stderr.length > 1000 ? '...(truncated)' : '') || 'Unknown error',\n                        code,\n                        slug\n                    });\n                }\n                resolve();\n            });\n            scriptProcess.on('error', (error)=>{\n                clearTimeout(timeout);\n                console.error(`Error launching process for ${slug}:`, error.message);\n                res.status(500).json({\n                    message: 'Error launching blog post processor',\n                    error: error.message,\n                    slug\n                });\n                resolve();\n            });\n        });\n    } catch (error) {\n        console.error('Error processing blog post:', error);\n        return res.status(500).json({\n            message: 'Error processing blog post',\n            error: String(error)\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/blog/process-post.ts\n");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fprocess-post&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cprocess-post.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();