"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/blog/update-post-metadata";
exports.ids = ["pages/api/blog/update-post-metadata"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fupdate-post-metadata&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cupdate-post-metadata.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fupdate-post-metadata&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cupdate-post-metadata.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_blog_update_post_metadata_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\blog\\update-post-metadata.ts */ \"(api-node)/./pages/api/blog/update-post-metadata.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_blog_update_post_metadata_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_blog_update_post_metadata_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/blog/update-post-metadata\",\n        pathname: \"/api/blog/update-post-metadata\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_blog_update_post_metadata_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fupdate-post-metadata&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cupdate-post-metadata.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/blog/update-post-metadata.ts":
/*!************************************************!*\
  !*** ./pages/api/blog/update-post-metadata.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gray_matter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gray-matter */ \"gray-matter\");\n/* harmony import */ var gray_matter__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(gray_matter__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Path to the blog posts directory\nconst BLOG_POSTS_PATH = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'content', 'blog', 'posts');\nasync function handler(req, res) {\n    // Only allow POST method\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            message: 'Method not allowed'\n        });\n    }\n    // Only allow in development mode\n    if (false) {}\n    try {\n        const { slug, draft, date } = req.body;\n        if (!slug) {\n            return res.status(400).json({\n                message: 'Slug is required'\n            });\n        }\n        // Find MDX file\n        const mdxPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(BLOG_POSTS_PATH, `${slug}.mdx`);\n        const mdPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(BLOG_POSTS_PATH, `${slug}.md`);\n        let filePath;\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(mdxPath)) {\n            filePath = mdxPath;\n        } else if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(mdPath)) {\n            filePath = mdPath;\n        } else {\n            return res.status(404).json({\n                message: `Blog post ${slug} not found`\n            });\n        }\n        // Read and parse the file\n        const content = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(filePath, 'utf8');\n        const { data, content: mdxContent } = gray_matter__WEBPACK_IMPORTED_MODULE_2___default()(content);\n        // Update metadata\n        const updatedData = {\n            ...data\n        };\n        let changed = false;\n        // Update draft status if provided\n        if (draft !== undefined && updatedData.draft !== draft) {\n            updatedData.draft = draft;\n            changed = true;\n        }\n        // Update date if provided\n        if (date && updatedData.date !== date) {\n            updatedData.date = date;\n            changed = true;\n        }\n        if (!changed) {\n            return res.status(200).json({\n                message: 'No changes detected'\n            });\n        }\n        // Write updated content back to file\n        const updatedFileContent = gray_matter__WEBPACK_IMPORTED_MODULE_2___default().stringify(mdxContent, updatedData);\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, updatedFileContent);\n        // Respond with success and updated data\n        return res.status(200).json({\n            message: 'Blog post metadata updated successfully',\n            data: {\n                slug,\n                draft: updatedData.draft,\n                date: updatedData.date\n            }\n        });\n    } catch (error) {\n        console.error('Error updating blog post metadata:', error);\n        return res.status(500).json({\n            message: 'Error updating blog post metadata',\n            error: String(error)\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/blog/update-post-metadata.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "gray-matter":
/*!******************************!*\
  !*** external "gray-matter" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("gray-matter");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fblog%2Fupdate-post-metadata&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cblog%5Cupdate-post-metadata.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();