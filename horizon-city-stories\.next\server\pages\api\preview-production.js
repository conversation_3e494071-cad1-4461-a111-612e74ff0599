"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/preview-production";
exports.ids = ["pages/api/preview-production"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpreview-production&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cpreview-production.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpreview-production&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cpreview-production.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_preview_production_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\preview-production.ts */ \"(api-node)/./pages/api/preview-production.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_preview_production_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_preview_production_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/preview-production\",\n        pathname: \"/api/preview-production\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_preview_production_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpreview-production&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cpreview-production.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/preview-production.ts":
/*!*****************************************!*\
  !*** ./pages/api/preview-production.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/**\n * API route that enables production view simulation in development mode\n * Only works in development mode for security reasons\n */ function handler(req, res) {\n    // Only allow in development mode\n    if (false) {}\n    // Set preview data with the productionView flag\n    res.setPreviewData({\n        ...req.preview ? req.previewData : {},\n        productionView: true\n    });\n    // Redirect back to the referring page or to the blog index\n    const redirectTo = req.query.redirectTo?.toString() || '/blog';\n    res.redirect(redirectTo);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/preview-production.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpreview-production&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cpreview-production.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();