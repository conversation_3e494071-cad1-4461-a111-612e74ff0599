"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_components_layout_DebugDisplay_tsx"],{

/***/ "(pages-dir-browser)/./components/layout/DebugDisplay.tsx":
/*!********************************************!*\
  !*** ./components/layout/DebugDisplay.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-browser)/./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_access_badges__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/access-badges */ \"(pages-dir-browser)/./utils/access-badges.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n// Client-side only KeysDisplay to avoid hydration errors\nconst KeysDisplay = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.resolve(_s(()=>{\n        _s();\n        const [hasAccess, setHasAccess] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n        react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n            \"KeysDisplay.useEffect\": ()=>{\n                // Check if user has any badges\n                const checkAccess = {\n                    \"KeysDisplay.useEffect.checkAccess\": async ()=>{\n                        const hasAccess = (0,_utils_access_badges__WEBPACK_IMPORTED_MODULE_3__.hasAnyBadge)();\n                        setHasAccess(hasAccess);\n                    }\n                }[\"KeysDisplay.useEffect.checkAccess\"];\n                checkAccess();\n                // Only set up interval if we're not on a story page\n                const isStoryPage = window.location.pathname.includes('/stories/');\n                if (!isStoryPage) {\n                    // Update access status periodically\n                    const interval = setInterval(checkAccess, 1000);\n                    return ({\n                        \"KeysDisplay.useEffect\": ()=>clearInterval(interval)\n                    })[\"KeysDisplay.useEffect\"];\n                }\n            }\n        }[\"KeysDisplay.useEffect\"], []);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                color: 'white',\n                fontSize: '8px',\n                textAlign: 'center',\n                marginTop: '5px'\n            },\n            children: [\n                \"Paydata Access: \",\n                hasAccess ? 'Granted' : 'Denied'\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n            lineNumber: 29,\n            columnNumber: 5\n        }, undefined);\n    }, \"epo/fOOQes5sKPzjcLlr/HdeIRw=\")), {\n    ssr: false\n});\n_c = KeysDisplay;\nconst DebugDisplay = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: '10px',\n            left: '10px',\n            zIndex: 9999,\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '5px',\n            background: 'rgba(0, 0, 0, 0.7)',\n            padding: '5px',\n            borderRadius: '4px',\n            border: '1px solid rgba(76, 29, 149, 0.5)',\n            maxWidth: '150px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '10px',\n                    textAlign: 'center',\n                    marginBottom: '5px',\n                    fontWeight: 'bold'\n                },\n                children: \"Paydata Debug\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KeysDisplay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\layout\\\\DebugDisplay.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = DebugDisplay;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DebugDisplay);\nvar _c, _c1;\n$RefreshReg$(_c, \"KeysDisplay\");\n$RefreshReg$(_c1, \"DebugDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/layout/DebugDisplay.tsx\n"));

/***/ })

}]);