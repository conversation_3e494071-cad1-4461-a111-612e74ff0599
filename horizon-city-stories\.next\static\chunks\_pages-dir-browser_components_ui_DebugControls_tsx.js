"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_components_ui_DebugControls_tsx"],{

/***/ "(pages-dir-browser)/./components/ui/DebugControls.tsx":
/*!*****************************************!*\
  !*** ./components/ui/DebugControls.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugControls)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/debug-manager */ \"(pages-dir-browser)/./utils/debug-manager.ts\");\n\nvar _s = $RefreshSig$();\n\n\n/**\n * Debug Controls Component\n *\n * This component provides a UI for controlling debug settings.\n * It's hidden by default and can be activated with a special key combination.\n */ function DebugControls() {\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [debugState, setDebugState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        enabled: false,\n        level: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.NONE\n    });\n    // Initialize state from debug manager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DebugControls.useEffect\": ()=>{\n            // Check for debug activation key combination\n            const handleKeyDown = {\n                \"DebugControls.useEffect.handleKeyDown\": (e)=>{\n                    // Ctrl+Shift+D to toggle debug controls\n                    if (e.ctrlKey && e.shiftKey && e.key === 'D') {\n                        setIsVisible({\n                            \"DebugControls.useEffect.handleKeyDown\": (prev)=>!prev\n                        }[\"DebugControls.useEffect.handleKeyDown\"]);\n                        e.preventDefault();\n                    }\n                }\n            }[\"DebugControls.useEffect.handleKeyDown\"];\n            window.addEventListener('keydown', handleKeyDown);\n            // Update state from debug manager\n            const state = (0,_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.getDebugState)();\n            setDebugState(state);\n            return ({\n                \"DebugControls.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"DebugControls.useEffect\"];\n        }\n    }[\"DebugControls.useEffect\"], []);\n    // Handle debug enable/disable\n    const handleToggleDebug = ()=>{\n        const newEnabled = !debugState.enabled;\n        (0,_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.setDebugEnabled)(newEnabled);\n        setDebugState((prev)=>({\n                ...prev,\n                enabled: newEnabled\n            }));\n    };\n    // Handle debug level change\n    const handleLevelChange = (e)=>{\n        const level = parseInt(e.target.value);\n        (0,_utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.setDebugLevel)(level);\n        setDebugState((prev)=>({\n                ...prev,\n                level\n            }));\n    };\n    // Handle clearing all badges\n    const handleClearBadges = ()=>{\n        if (window.confirm('Are you sure you want to clear all access badges? This will reset your progress.')) {\n            _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.BadgeDebugger.clearAllBadges();\n            window.location.reload();\n        }\n    };\n    // Handle viewing all badges\n    const handleViewBadges = ()=>{\n        _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.BadgeDebugger.logAllBadges();\n        alert('Badges logged to console. Check browser console (F12) to view them.');\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            bottom: '20px',\n            right: '20px',\n            zIndex: 9999,\n            backgroundColor: '#1A202C',\n            padding: '12px',\n            borderRadius: '6px',\n            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n            border: '1px solid #2D3748',\n            maxWidth: '300px',\n            color: 'white',\n            fontFamily: 'sans-serif'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontWeight: 'bold',\n                    marginBottom: '8px',\n                    color: '#4FD1C5'\n                },\n                children: \"Debug Controls (Ctrl+Shift+D)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '12px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"debug-toggle\",\n                                style: {\n                                    fontSize: '14px'\n                                },\n                                children: \"Debug Mode\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"debug-toggle\",\n                                type: \"checkbox\",\n                                checked: debugState.enabled,\n                                onChange: handleToggleDebug\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '4px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"debug-level\",\n                                style: {\n                                    fontSize: '14px'\n                                },\n                                children: \"Debug Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"debug-level\",\n                                value: debugState.level,\n                                onChange: handleLevelChange,\n                                disabled: !debugState.enabled,\n                                style: {\n                                    padding: '4px 8px',\n                                    backgroundColor: '#2D3748',\n                                    border: '1px solid #4A5568',\n                                    borderRadius: '4px',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.NONE,\n                                        children: \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.ERROR,\n                                        children: \"Error\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.WARN,\n                                        children: \"Warning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.INFO,\n                                        children: \"Info\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _utils_debug_manager__WEBPACK_IMPORTED_MODULE_2__.DebugLevel.VERBOSE,\n                                        children: \"Verbose\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleViewBadges,\n                                disabled: !debugState.enabled,\n                                style: {\n                                    flex: 1,\n                                    padding: '6px 12px',\n                                    backgroundColor: debugState.enabled ? '#3182CE' : '#2D3748',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    color: 'white',\n                                    cursor: debugState.enabled ? 'pointer' : 'not-allowed'\n                                },\n                                children: \"View Badges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearBadges,\n                                disabled: !debugState.enabled,\n                                style: {\n                                    flex: 1,\n                                    padding: '6px 12px',\n                                    backgroundColor: debugState.enabled ? '#E53E3E' : '#2D3748',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    color: 'white',\n                                    cursor: debugState.enabled ? 'pointer' : 'not-allowed'\n                                },\n                                children: \"Clear Badges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DebugControls.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugControls, \"sn0YeiIdy7GdWxy0gYGT3WrBb9I=\");\n_c = DebugControls;\nvar _c;\n$RefreshReg$(_c, \"DebugControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/ui/DebugControls.tsx\n"));

/***/ })

}]);