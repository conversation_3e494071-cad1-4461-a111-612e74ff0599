"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_components_ui_Navigation_tsx"],{

/***/ "(pages-dir-browser)/./components/ui/DesktopNav.tsx":
/*!**************************************!*\
  !*** ./components/ui/DesktopNav.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AppLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AppLink */ \"(pages-dir-browser)/./components/ui/AppLink.tsx\");\n\n\n\n/**\n * Desktop navigation component\n */ const DesktopNav = (param)=>{\n    let { items } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"desktop-nav\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"flex gap-8\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: item.href,\n                        className: \"font-rajdhani font-medium text-lg transition-all\",\n                        style: {\n                            textShadow: item.style.textShadow,\n                            color: item.style.color,\n                            position: 'relative',\n                            padding: '0.25rem 0.5rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    position: 'absolute',\n                                    bottom: '-2px',\n                                    left: '0',\n                                    width: '100%',\n                                    height: '2px',\n                                    background: \"linear-gradient(to right, transparent, \".concat(item.style.borderColor || 'rgba(139, 92, 246, 0.7)', \", transparent)\"),\n                                    transform: 'scaleX(0.7)',\n                                    opacity: '0.7',\n                                    transition: 'all 0.3s ease'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, undefined),\n                            item.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 13\n                    }, undefined)\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\DesktopNav.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = DesktopNav;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DesktopNav);\nvar _c;\n$RefreshReg$(_c, \"DesktopNav\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/ui/DesktopNav.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/ui/MobileMenu.tsx":
/*!**************************************!*\
  !*** ./components/ui/MobileMenu.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(pages-dir-browser)/./node_modules/react-dom/index.js\");\n/* harmony import */ var _AppLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AppLink */ \"(pages-dir-browser)/./components/ui/AppLink.tsx\");\n\n\n\n\n/**\n * Mobile menu component that renders directly into the body\n */ const MobileMenu = (param)=>{\n    let { isOpen, items, onNavClick } = param;\n    if (!isOpen || typeof document === 'undefined') return null;\n    // Render directly to body to avoid any container issues\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"mobile-menu-overlay\",\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 9000,\n            background: 'transparent',\n            pointerEvents: 'none',\n            overflow: 'hidden'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mobile-menu\",\n            style: {\n                position: 'fixed',\n                top: '64px',\n                right: '16px',\n                background: 'linear-gradient(to bottom, rgba(20, 10, 50, 0.98), rgba(13, 6, 32, 0.98))',\n                borderBottom: '1px solid rgba(76, 29, 149, 0.5)',\n                borderLeft: '1px solid rgba(76, 29, 149, 0.5)',\n                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.7)',\n                zIndex: 9999,\n                padding: '16px',\n                width: '250px',\n                maxHeight: 'calc(100vh - 80px)',\n                overflowY: 'auto',\n                overflowX: 'hidden',\n                transition: 'opacity 0.3s ease',\n                opacity: 1,\n                pointerEvents: 'auto',\n                borderRadius: '0 0 12px 12px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"flex flex-col gap-3 w-full\",\n                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"w-full text-right\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: item.href,\n                            className: \"font-rajdhani font-medium text-lg transition-all block py-2 px-4 w-full text-right\",\n                            style: {\n                                textShadow: item.style.textShadow,\n                                color: item.style.color,\n                                borderBottom: \"1px solid \".concat(item.style.borderColor || 'rgba(139, 92, 246, 0.3)'),\n                                textAlign: 'right',\n                                direction: 'rtl'\n                            },\n                            onClick: onNavClick,\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenu.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined), document.body);\n};\n_c = MobileMenu;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileMenu);\nvar _c;\n$RefreshReg$(_c, \"MobileMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/ui/MobileMenu.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/ui/MobileMenuButton.tsx":
/*!********************************************!*\
  !*** ./components/ui/MobileMenuButton.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * Hamburger button component for mobile navigation\n */ const MobileMenuButton = (param)=>{\n    let { isOpen, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"hamburger-btn\",\n        style: {\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: 'center',\n            gap: '6px',\n            backgroundColor: 'transparent',\n            border: 'none',\n            cursor: 'pointer',\n            zIndex: 50,\n            position: 'relative',\n            padding: '8px',\n            borderRadius: '4px',\n            background: isOpen ? 'rgba(76, 29, 149, 0.3)' : 'transparent',\n            transition: 'background 0.3s ease'\n        },\n        \"aria-label\": \"Toggle menu\",\n        \"aria-expanded\": isOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: isOpen ? '22px' : '24px',\n                    height: '2px',\n                    background: 'linear-gradient(to right, #22d3ee, #818cf8)',\n                    boxShadow: '0 0 5px rgba(139, 92, 246, 0.7)',\n                    transition: 'transform 0.3s ease, width 0.2s ease',\n                    transform: isOpen ? 'rotate(45deg) translate(5px, 5px)' : 'rotate(0)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '24px',\n                    height: '2px',\n                    background: 'linear-gradient(to right, #22d3ee, #818cf8)',\n                    boxShadow: '0 0 5px rgba(139, 92, 246, 0.7)',\n                    transition: 'opacity 0.2s ease, transform 0.2s ease',\n                    opacity: isOpen ? 0 : 1,\n                    transform: isOpen ? 'translateX(-5px)' : 'translateX(0)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: isOpen ? '22px' : '24px',\n                    height: '2px',\n                    background: 'linear-gradient(to right, #22d3ee, #818cf8)',\n                    boxShadow: '0 0 5px rgba(139, 92, 246, 0.7)',\n                    transition: 'transform 0.3s ease, width 0.2s ease',\n                    transform: isOpen ? 'rotate(-45deg) translate(5px, -5px)' : 'rotate(0)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\MobileMenuButton.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MobileMenuButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileMenuButton);\nvar _c;\n$RefreshReg$(_c, \"MobileMenuButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/ui/MobileMenuButton.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/ui/NavItems.ts":
/*!***********************************!*\
  !*** ./components/ui/NavItems.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAV_ITEMS: () => (/* binding */ NAV_ITEMS)\n/* harmony export */ });\n/**\n * TypeScript interface for NavItem\n */ /**\n * Centralized navigation config\n */ const NAV_ITEMS = [\n    {\n        href: \"/blog\",\n        label: \"I HATE IT HERE\",\n        style: {\n            color: '#fef08a',\n            textShadow: '0 0 10px rgba(234, 179, 8, 1), 0 0 15px rgba(234, 179, 8, 0.8)',\n            borderColor: 'rgba(234, 179, 8, 0.3)'\n        }\n    },\n    {\n        href: \"/stories\",\n        label: \"STORIES\",\n        style: {\n            color: '#d6bcfa',\n            textShadow: '0 0 10px rgba(139, 92, 246, 1), 0 0 15px rgba(139, 92, 246, 0.8)',\n            borderColor: 'rgba(139, 92, 246, 0.3)'\n        }\n    },\n    {\n        href: \"/paydata\",\n        label: \"PAYDATA\",\n        style: {\n            color: '#d6bcfa',\n            textShadow: '0 0 10px rgba(139, 92, 246, 1), 0 0 15px rgba(139, 92, 246, 0.8)',\n            borderColor: 'rgba(139, 92, 246, 0.3)'\n        }\n    },\n    {\n        href: \"/outreach\",\n        label: \"OUTREACH\",\n        style: {\n            color: '#22d3ee',\n            textShadow: '0 0 10px rgba(34, 211, 238, 0.7)',\n            borderColor: 'rgba(34, 211, 238, 0.3)'\n        }\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/ui/NavItems.ts\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/ui/Navigation.tsx":
/*!**************************************!*\
  !*** ./components/ui/Navigation.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _DesktopNav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DesktopNav */ \"(pages-dir-browser)/./components/ui/DesktopNav.tsx\");\n/* harmony import */ var _MobileMenuButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MobileMenuButton */ \"(pages-dir-browser)/./components/ui/MobileMenuButton.tsx\");\n/* harmony import */ var _MobileMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileMenu */ \"(pages-dir-browser)/./components/ui/MobileMenu.tsx\");\n/* harmony import */ var _NavItems__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NavItems */ \"(pages-dir-browser)/./components/ui/NavItems.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Main Navigation component that handles responsive behavior\n */ const Navigation = ()=>{\n    _s();\n    // State for mobile menu\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State to track viewport width\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // State to track if we're on client side\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Define breakpoint\n    const mobileBreakpoint = 700;\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            // Mark as mounted to signal client-side rendering\n            setIsMounted(true);\n            // Only set window width on client-side\n            if (true) {\n                // Set initial width\n                setWindowWidth(window.innerWidth);\n                const handleResize = {\n                    \"Navigation.useEffect.handleResize\": ()=>{\n                        const width = window.innerWidth;\n                        setWindowWidth(width);\n                        // Close menu when resizing to desktop\n                        if (width > mobileBreakpoint) {\n                            setIsMenuOpen(false);\n                        }\n                    }\n                }[\"Navigation.useEffect.handleResize\"];\n                // Add event listeners for resize and menu close events\n                window.addEventListener('resize', handleResize);\n                document.addEventListener('closeNavigationMenu', {\n                    \"Navigation.useEffect\": ()=>setIsMenuOpen(false)\n                }[\"Navigation.useEffect\"]);\n                // Clean up\n                return ({\n                    \"Navigation.useEffect\": ()=>{\n                        window.removeEventListener('resize', handleResize);\n                        document.removeEventListener('closeNavigationMenu', {\n                            \"Navigation.useEffect\": ()=>setIsMenuOpen(false)\n                        }[\"Navigation.useEffect\"]);\n                    }\n                })[\"Navigation.useEffect\"];\n            }\n        }\n    }[\"Navigation.useEffect\"], []);\n    // Handle mobile menu toggle\n    const toggleMenu = ()=>{\n        const newIsOpen = !isMenuOpen;\n        setIsMenuOpen(newIsOpen);\n        // Toggle body class to prevent scrolling when menu is open\n        if (typeof document !== 'undefined') {\n            if (newIsOpen) {\n                document.body.classList.add('menu-open');\n            } else {\n                document.body.classList.remove('menu-open');\n            }\n        }\n    };\n    // Close menu when a link is clicked\n    const handleNavClick = ()=>{\n        setIsMenuOpen(false);\n        // Remove the menu-open class from body\n        if (typeof document !== 'undefined') {\n            document.body.classList.remove('menu-open');\n        }\n    };\n    // Determine if we're in mobile view\n    const isMobileView = windowWidth <= mobileBreakpoint;\n    // Only render on the client side\n    if (!isMounted) {\n        return null; // Return null during SSR to avoid hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            !isMobileView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DesktopNav__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                items: _NavItems__WEBPACK_IMPORTED_MODULE_5__.NAV_ITEMS\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                lineNumber: 89,\n                columnNumber: 25\n            }, undefined),\n            isMobileView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-nav-container\",\n                style: {\n                    position: 'relative',\n                    zIndex: 40\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileMenuButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        isOpen: isMenuOpen,\n                        onClick: toggleMenu\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileMenu__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isMenuOpen,\n                        items: _NavItems__WEBPACK_IMPORTED_MODULE_5__.NAV_ITEMS,\n                        onNavClick: handleNavClick\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\horizon-city-stories\\\\components\\\\ui\\\\Navigation.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Navigation, \"q5Jm13Xpngs4/sT2335AMhPwnJ8=\");\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/ui/Navigation.tsx\n"));

/***/ })

}]);