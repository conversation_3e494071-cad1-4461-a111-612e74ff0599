{"c": ["webpack"], "r": ["pages/blog/[id]"], "m": ["(pages-dir-browser)/./components/blog/BlogContent.tsx", "(pages-dir-browser)/./components/mdx/MDXComponents.tsx", "(pages-dir-browser)/./components/ui/ContentDetailPageV3.tsx", "(pages-dir-browser)/./components/ui/NextChakraLink.tsx", "(pages-dir-browser)/./node_modules/@chakra-ui/react/dist/esm/components/code/code.js", "(pages-dir-browser)/./node_modules/@mdx-js/react/index.js", "(pages-dir-browser)/./node_modules/@mdx-js/react/lib/index.js", "(pages-dir-browser)/./node_modules/next-mdx-remote/dist/idle-callback-polyfill.js", "(pages-dir-browser)/./node_modules/next-mdx-remote/dist/index.js", "(pages-dir-browser)/./node_modules/next-mdx-remote/dist/jsx-runtime.cjs", "(pages-dir-browser)/./node_modules/next-mdx-remote/index.js", "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CT%5CProjects%5Chorizon-city-stories%5Cpages%5Cblog%5C%5Bid%5D.tsx&page=%2Fblog%2F%5Bid%5D!", "(pages-dir-browser)/./pages/blog/[id].tsx"]}